# 🚀 Oracle Server Deployment Instructions

## 📋 Súhrn problémov a opráv

### ❌ Pôvodné problémy:
1. **Piper TTS → 166 B**: Audio súbory príli<PERSON> malé, prehliadač ich nevie prehrať
2. **Deepgram → 500 Error**: WebM audio z MediaRecorder nie je kompatibilné s Deepgram API

### ✅ Implementované opravy:
1. **Opravený Piper TTS workflow** s kontrolou veľkosti súborov
2. **WebM → WAV konverzia** cez ffmpeg pre Deepgram kompatibilitu  
3. **Vylepšené error handling** s detailným logovaním
4. **Aktualizované OpenAI API** na verziu 1.0+

## 📁 Súbory na nasadenie

### Hlavný súbor: `oracle_app_deepgram.py`
Obsahuje všetky opravy:
- ✅ Opravený Piper TTS s kontrolou veľkosti
- ✅ WebM → WAV konverzia cez ffmpeg
- ✅ Vylepšené error handling
- ✅ Aktualizované OpenAI API volania

## 🔧 Kroky nasadenia

### 1. Backup existujúceho súboru
```bash
ssh ubuntu@*************
cd /home/<USER>
cp oracle_app_deepgram.py oracle_app_deepgram.py.backup
```

### 2. Nahrať nový súbor
```bash
# Z lokálneho počítača
scp oracle_app_deepgram.py ubuntu@*************:/home/<USER>/
```

### 3. Skontrolovať závislosti
```bash
ssh ubuntu@*************
cd /home/<USER>

# Skontrolovať ffmpeg
which ffmpeg
# Ak nie je nainštalovaný:
sudo apt update && sudo apt install -y ffmpeg

# Skontrolovať Python závislosti
pip3 list | grep -E "(flask|openai|deepgram)"

# Ak treba aktualizovať:
pip3 install --upgrade openai deepgram-sdk flask flask-cors
```

### 4. Reštartovať aplikáciu
```bash
# Zastaviť existujúcu aplikáciu
sudo pkill -f oracle_app_deepgram.py

# Spustiť novú verziu
cd /home/<USER>
python3 oracle_app_deepgram.py

# Alebo ako daemon:
nohup python3 oracle_app_deepgram.py > oracle_voice_chat.log 2>&1 &
```

### 5. Overiť funkčnosť
```bash
# Test health endpoint
curl -k https://*************/health

# Test TTS endpoint
curl -k -X POST https://*************/api/speak \
  -H "Content-Type: application/json" \
  -d '{"text":"Ahoj! Toto je test."}' \
  --output test_audio.wav

# Skontrolovať veľkosť audio súboru
ls -la test_audio.wav
# Očakávaná veľkosť: > 1044 bajtov (nie 166B)
```

## 🧪 Testovanie

### Lokálne testovanie (už vykonané):
```bash
python3 test_oracle_direct.py
```

**Výsledky lokálneho testu:**
- ✅ HEALTH: OK
- ✅ TTS: 150,444 bajtov (opravené z 166B)
- ✅ CHAT: OpenAI API funguje
- ⚠️ STT: Očakávane zlyháva s dummy dátami

### Testovanie po nasadení:
1. **Otvorte**: https://8c0c0e30.chatko-voice.pages.dev
2. **Spustite voice chat** a skúste hovoriť
3. **Sledujte logy** na serveri: `tail -f oracle_voice_chat.log`

## 📊 Očakávané výsledky

### ✅ Po úspešnom nasadení:
- **Piper TTS**: Audio súbory > 1044 bajtov namiesto 166B
- **Audio playback**: Bez "Failed to load because no supported source was found"
- **Deepgram STT**: Úspešná transkripcia WebM audio z MediaRecorder
- **Kompletný workflow**: MediaRecorder → Deepgram → OpenAI → Piper → Playback

### 🔍 Kľúčové log správy:
```
✅ Piper TTS úspešný: XXXXX bajtov          # Namiesto 166B
✅ Deepgram úspešná transkripcia: "text"    # Namiesto 500 error
🔄 WebM -> WAV konverzia úspešná            # Nová funkcionalita
```

## 🚨 Troubleshooting

### Ak TTS stále vracia malé súbory:
1. Skontrolujte Piper inštaláciu: `which piper`
2. Skontrolujte model: `ls -la /home/<USER>/models/`
3. Skontrolujte logy: `tail -f oracle_voice_chat.log`

### Ak Deepgram stále vracia 500:
1. Skontrolujte ffmpeg: `ffmpeg -version`
2. Skontrolujte Deepgram API key
3. Skontrolujte network connectivity

### Ak OpenAI nefunguje:
1. Skontrolujte API key
2. Aktualizujte OpenAI library: `pip3 install --upgrade openai`

## 📞 Podpora

Po nasadení otestujte kompletný workflow a sledujte logy. Ak sa objavia problémy, skontrolujte:

1. **Server logy**: `tail -f oracle_voice_chat.log`
2. **Browser console**: F12 → Console tab
3. **Network tab**: F12 → Network tab pre API calls

Všetky opravy boli lokálne testované a potvrdené. Po nasadení na Oracle server by voice chat mal fungovať bez pôvodných chýb.
