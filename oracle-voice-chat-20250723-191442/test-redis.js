#!/usr/bin/env node

/**
 * Test script for Upstash Redis connection
 */

require('dotenv').config();
const { Redis } = require('@upstash/redis');

async function testRedisConnection() {
    console.log('🔍 Testing Upstash Redis connection...');
    
    try {
        // Initialize Redis client
        const redis = new Redis({
            url: process.env.UPSTASH_REDIS_REST_URL,
            token: process.env.UPSTASH_REDIS_REST_TOKEN,
        });
        
        console.log('✅ Redis client initialized');
        
        // Test basic operations
        const testKey = 'test:connection';
        const testValue = { message: 'Hello from Upstash!', timestamp: Date.now() };
        
        // Set a test value
        console.log('📝 Setting test value...');
        await redis.setex(testKey, 60, JSON.stringify(testValue));
        console.log('✅ Test value set successfully');
        
        // Get the test value
        console.log('📖 Getting test value...');
        const retrievedValue = await redis.get(testKey);
        console.log('✅ Test value retrieved:', JSON.parse(retrievedValue));
        
        // Delete the test value
        console.log('🗑️ Cleaning up test value...');
        await redis.del(testKey);
        console.log('✅ Test value deleted');
        
        // Test session-like operations
        const sessionId = 'test-session-123';
        const sessionData = {
            id: sessionId,
            created: Date.now(),
            lastActivity: Date.now(),
            conversationHistory: [
                { role: 'user', content: 'Hello!', timestamp: Date.now() },
                { role: 'assistant', content: 'Hi there!', timestamp: Date.now() }
            ],
            requestCount: 2,
            status: 'active'
        };
        
        console.log('💾 Testing session storage...');
        await redis.setex(`session:${sessionId}`, 3600, JSON.stringify(sessionData));
        
        const retrievedSession = await redis.get(`session:${sessionId}`);
        const parsedSession = JSON.parse(retrievedSession);
        
        console.log('✅ Session stored and retrieved successfully');
        console.log('📊 Session data:', {
            id: parsedSession.id,
            requestCount: parsedSession.requestCount,
            conversationLength: parsedSession.conversationHistory.length
        });
        
        // Cleanup
        await redis.del(`session:${sessionId}`);
        console.log('🧹 Session cleanup completed');
        
        console.log('🎉 All Redis tests passed successfully!');
        
    } catch (error) {
        console.error('❌ Redis connection test failed:', error);
        process.exit(1);
    }
}

// Run the test
testRedisConnection();
