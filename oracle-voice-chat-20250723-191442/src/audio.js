/**
 * Optimized Audio Processor
 * High-performance audio processing and validation
 */

class AudioProcessor {
    constructor() {
        this.sampleRate = 16000; // Standard for speech recognition
        this.channels = 1;       // Mono audio
        this.bitDepth = 16;      // 16-bit samples
        
        console.log('🎵 Audio processor initialized');
    }
    
    async process(audioBuffer) {
        const startTime = Date.now();

        try {
            // Validate audio
            this.validateAudio(audioBuffer);

            // Enhanced audio processing pipeline
            let processed = audioBuffer;

            // Step 1: Normalize audio levels
            processed = await this.normalizeAudio(processed);

            // Step 2: Apply noise reduction
            processed = await this.applyNoiseReduction(processed);

            // Step 3: Ensure correct format for STT
            processed = await this.ensureFormat(processed);

            // Step 4: Trim silence from beginning and end
            processed = await this.trimSilence(processed);

            // Log processing stats
            const duration = Date.now() - startTime;
            const originalSize = audioBuffer.length;
            const processedSize = processed.length;
            const compressionRatio = ((originalSize - processedSize) / originalSize * 100).toFixed(1);

            console.log(`🎵 Audio processed in ${duration}ms (${processedSize} bytes, ${compressionRatio}% reduction)`);

            return processed;

        } catch (error) {
            console.error('Audio processing error:', error.message);
            throw new Error(`Audio processing failed: ${error.message}`);
        }
    }
    
    validateAudio(audioBuffer) {
        if (!Buffer.isBuffer(audioBuffer)) {
            throw new Error('Audio data must be a Buffer');
        }
        
        if (audioBuffer.length === 0) {
            throw new Error('Empty audio data');
        }
        
        if (audioBuffer.length < 1000) {
            console.warn(`Audio data is very short (${audioBuffer.length} bytes)`);
        }
        
        // Basic format detection
        const format = this.detectFormat(audioBuffer);
        console.log(`🎵 Detected audio format: ${format}`);
        
        return true;
    }
    
    detectFormat(audioBuffer) {
        if (audioBuffer.length < 4) {
            return 'unknown';
        }
        
        const header = audioBuffer.subarray(0, 4);
        
        if (header.equals(Buffer.from('RIFF'))) {
            return 'wav';
        } else if (header.equals(Buffer.from('OggS'))) {
            return 'ogg';
        } else if (audioBuffer.subarray(0, 4).includes(Buffer.from('webm')[0])) {
            return 'webm';
        } else {
            return 'unknown';
        }
    }
    
    async normalizeAudio(audioBuffer) {
        // Basic audio normalization
        // In a production system, this would use proper audio processing libraries
        
        console.log('🔄 Normalizing audio...');
        
        // For now, just return the original buffer
        // TODO: Implement proper audio normalization using FFmpeg or similar
        return audioBuffer;
    }
    
    async ensureFormat(audioBuffer) {
        // Ensure audio is in the correct format for STT
        console.log(`🔄 Ensuring audio format (${this.sampleRate}Hz, ${this.channels} channels)`);
        
        const format = this.detectFormat(audioBuffer);
        
        switch (format) {
            case 'wav':
                return audioBuffer; // Already in good format
                
            case 'webm':
            case 'ogg':
                // TODO: Convert to WAV using FFmpeg
                console.warn('Audio conversion not implemented, using original data');
                return audioBuffer;
                
            default:
                console.warn('Unknown audio format, attempting to process as-is');
                return audioBuffer;
        }
    }
    
    getAudioInfo(audioBuffer) {
        return {
            size: audioBuffer.length,
            format: this.detectFormat(audioBuffer),
            estimatedDuration: this.estimateDuration(audioBuffer),
            sampleRate: this.sampleRate,
            channels: this.channels,
            bitDepth: this.bitDepth
        };
    }
    
    estimateDuration(audioBuffer) {
        // Rough estimation based on file size
        // Assumes 16kHz, 16-bit, mono audio
        const bytesPerSecond = this.sampleRate * (this.bitDepth / 8) * this.channels;
        const durationSeconds = audioBuffer.length / bytesPerSecond;
        return Math.round(durationSeconds * 1000); // Return in milliseconds
    }
    
    splitAudioChunks(audioBuffer, chunkSizeMs = 1000) {
        // Split audio into chunks for streaming processing
        const bytesPerMs = (this.sampleRate * (this.bitDepth / 8) * this.channels) / 1000;
        const chunkSizeBytes = Math.floor(chunkSizeMs * bytesPerMs);
        
        const chunks = [];
        let offset = 0;
        
        while (offset < audioBuffer.length) {
            const end = Math.min(offset + chunkSizeBytes, audioBuffer.length);
            chunks.push(audioBuffer.subarray(offset, end));
            offset = end;
        }
        
        console.log(`🔪 Split audio into ${chunks.length} chunks of ~${chunkSizeMs}ms each`);
        return chunks;
    }
    
    mergeAudioChunks(chunks) {
        // Merge audio chunks back together
        const totalSize = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const merged = Buffer.concat(chunks, totalSize);
        
        console.log(`🔗 Merged ${chunks.length} chunks into ${merged.length} bytes`);
        return merged;
    }
    
    // Enhanced noise reduction and audio cleaning
    async applyNoiseReduction(audioBuffer) {
        console.log('🔇 Applying advanced noise reduction...');

        try {
            // Basic noise gate - remove very quiet sections
            const processed = this.applyNoiseGate(audioBuffer);

            // Apply simple high-pass filter to remove low-frequency noise
            const filtered = this.applyHighPassFilter(processed);

            console.log(`🔇 Noise reduction applied: ${audioBuffer.length} → ${filtered.length} bytes`);
            return filtered;

        } catch (error) {
            console.warn('Noise reduction failed, using original audio:', error.message);
            return audioBuffer;
        }
    }

    applyNoiseGate(audioBuffer) {
        // Enhanced noise gate implementation with speech detection
        const threshold = 800; // Higher threshold for better noise filtering
        const processed = Buffer.from(audioBuffer);
        let speechSamples = 0;
        let totalSamples = 0;

        // First pass - analyze audio content
        for (let i = 0; i < processed.length - 1; i += 2) {
            const sample = processed.readInt16LE(i);
            const amplitude = Math.abs(sample);
            totalSamples++;

            if (amplitude > threshold) {
                speechSamples++;
            }
        }

        // Calculate speech ratio
        const speechRatio = speechSamples / totalSamples;
        console.log(`🔍 Audio analysis: ${speechRatio.toFixed(3)} speech ratio (${speechSamples}/${totalSamples})`);

        // If less than 5% of samples are above threshold, likely noise
        if (speechRatio < 0.05) {
            console.log(`🔇 Low speech ratio detected - likely background noise`);
            // Return very quiet audio to indicate noise
            const quietBuffer = Buffer.alloc(processed.length);
            return quietBuffer;
        }

        // Second pass - apply noise gate
        for (let i = 0; i < processed.length - 1; i += 2) {
            const sample = processed.readInt16LE(i);
            const amplitude = Math.abs(sample);

            // If below threshold, reduce amplitude significantly
            if (amplitude < threshold) {
                const reducedSample = Math.floor(sample * 0.05); // Reduce to 5%
                processed.writeInt16LE(reducedSample, i);
            }
        }

        console.log(`🔊 Noise gate applied: speech ratio ${speechRatio.toFixed(3)}`);
        return processed;
    }

    applyHighPassFilter(audioBuffer) {
        // Simple high-pass filter to remove low-frequency noise
        const processed = Buffer.from(audioBuffer);
        let previousSample = 0;

        // Process 16-bit samples
        for (let i = 0; i < processed.length - 1; i += 2) {
            const currentSample = processed.readInt16LE(i);

            // Simple high-pass filter: output = input - previous_input * 0.95
            const filteredSample = Math.floor(currentSample - previousSample * 0.95);
            processed.writeInt16LE(Math.max(-32768, Math.min(32767, filteredSample)), i);

            previousSample = currentSample;
        }

        return processed;
    }

    async trimSilence(audioBuffer) {
        console.log('✂️ Trimming silence from audio...');

        try {
            const silenceThreshold = 1000; // Amplitude threshold for silence
            const minSilenceDuration = 0.1; // 100ms minimum silence to trim
            const sampleRate = this.sampleRate;
            const bytesPerSample = 2; // 16-bit
            const samplesPerSecond = sampleRate;
            const minSilenceSamples = Math.floor(minSilenceDuration * samplesPerSecond);

            // Find start of speech (trim leading silence)
            let startIndex = 0;
            let silenceCount = 0;

            for (let i = 0; i < audioBuffer.length - 1; i += bytesPerSample) {
                const sample = Math.abs(audioBuffer.readInt16LE(i));

                if (sample > silenceThreshold) {
                    // Found speech, stop trimming
                    break;
                } else {
                    silenceCount++;
                    if (silenceCount > minSilenceSamples) {
                        startIndex = i;
                    }
                }
            }

            // Find end of speech (trim trailing silence)
            let endIndex = audioBuffer.length;
            silenceCount = 0;

            for (let i = audioBuffer.length - bytesPerSample; i >= 0; i -= bytesPerSample) {
                const sample = Math.abs(audioBuffer.readInt16LE(i));

                if (sample > silenceThreshold) {
                    // Found speech, stop trimming
                    break;
                } else {
                    silenceCount++;
                    if (silenceCount > minSilenceSamples) {
                        endIndex = i + bytesPerSample;
                    }
                }
            }

            // Extract the trimmed audio
            const trimmed = audioBuffer.slice(startIndex, endIndex);

            const originalDuration = (audioBuffer.length / bytesPerSample / samplesPerSecond * 1000).toFixed(0);
            const trimmedDuration = (trimmed.length / bytesPerSample / samplesPerSecond * 1000).toFixed(0);

            console.log(`✂️ Silence trimmed: ${originalDuration}ms → ${trimmedDuration}ms`);

            return trimmed;

        } catch (error) {
            console.warn('Silence trimming failed, using original audio:', error.message);
            return audioBuffer;
        }
    }
    
    // Convert audio format using FFmpeg (if available)
    async convertToWav(audioBuffer) {
        const format = this.detectFormat(audioBuffer);
        
        if (format === 'wav') {
            return audioBuffer; // Already WAV
        }
        
        console.warn(`Audio conversion from ${format} to WAV not implemented`);
        return audioBuffer;
    }
    
    // Health check
    async healthCheck() {
        try {
            const testBuffer = Buffer.alloc(1024);
            await this.process(testBuffer);
            return true;
        } catch (error) {
            console.error('Audio processor health check failed:', error.message);
            return false;
        }
    }
    
    // Get processor statistics
    getStats() {
        return {
            sampleRate: this.sampleRate,
            channels: this.channels,
            bitDepth: this.bitDepth,
            supportedFormats: ['wav', 'webm', 'ogg']
        };
    }
}

module.exports = AudioProcessor;
