#!/usr/bin/env node

/**
 * Enhanced Session Manager for Fast Voice Server
 * Supports multiple concurrent sessions with Upstash Redis backing
 */

const { v4: uuidv4 } = require('uuid');
const NodeCache = require('node-cache');
const { Redis } = require('@upstash/redis');

class SessionManager {
    constructor(options = {}) {
        this.useRedis = options.useRedis || process.env.USE_REDIS === 'true';
        this.sessionTTL = options.sessionTTL || 3600; // 1 hour default

        // Initialize Upstash Redis client if enabled
        if (this.useRedis) {
            try {
                // Check if we have the required environment variables
                if (!process.env.UPSTASH_REDIS_REST_URL || !process.env.UPSTASH_REDIS_REST_TOKEN) {
                    throw new Error('Missing UPSTASH_REDIS_REST_URL or UPSTASH_REDIS_REST_TOKEN environment variables');
                }

                // Validate URL format
                if (process.env.UPSTASH_REDIS_REST_URL.includes('your-redis-endpoint')) {
                    throw new Error('Please replace the placeholder URL with your actual Upstash Redis REST URL');
                }

                this.redisClient = new Redis({
                    url: process.env.UPSTASH_REDIS_REST_URL,
                    token: process.env.UPSTASH_REDIS_REST_TOKEN,
                });
                console.log(`✅ Upstash Redis client initialized with URL: ${process.env.UPSTASH_REDIS_REST_URL}`);
            } catch (error) {
                console.error(`❌ Failed to initialize Upstash Redis:`, error.message);
                console.log(`📝 Falling back to local cache. To use Redis:`);
                console.log(`   1. Get your REST URL from Upstash dashboard`);
                console.log(`   2. Update UPSTASH_REDIS_REST_URL in .env file`);
                console.log(`   3. Restart the server`);
                this.useRedis = false;
                this.redisClient = null;
            }
        } else {
            this.redisClient = null;
        }

        // Fallback to in-memory cache if Redis not available
        this.localCache = new NodeCache({
            stdTTL: this.sessionTTL,
            checkperiod: 120 // Check for expired sessions every 2 minutes
        });

        // Track active sessions
        this.activeSessions = new Map();
        this.sessionMetrics = new Map();

        console.log(`📋 Session Manager initialized (Redis: ${this.useRedis})`);
    }
    
    /**
     * Create a new session
     */
    async createSession(clientInfo = {}) {
        const sessionId = uuidv4();
        const timestamp = Date.now();
        
        const sessionData = {
            id: sessionId,
            created: timestamp,
            lastActivity: timestamp,
            clientInfo,
            conversationHistory: [],
            requestCount: 0,
            totalLatency: 0,
            status: 'active'
        };
        
        // Store session data
        await this.setSessionData(sessionId, sessionData);
        
        // Track active session
        this.activeSessions.set(sessionId, {
            created: timestamp,
            lastActivity: timestamp,
            requestCount: 0
        });
        
        console.log(`✅ New session created: ${sessionId}`);
        return sessionId;
    }
    
    /**
     * Get session data
     */
    async getSessionData(sessionId) {
        try {
            if (this.useRedis && this.redisClient) {
                const data = await this.redisClient.get(`session:${sessionId}`);
                return data ? JSON.parse(data) : null;
            } else {
                return this.localCache.get(sessionId);
            }
        } catch (error) {
            console.error(`❌ Error getting session ${sessionId}:`, error);
            // Fallback to local cache if Redis fails
            return this.localCache.get(sessionId);
        }
    }
    
    /**
     * Set session data
     */
    async setSessionData(sessionId, data) {
        try {
            if (this.useRedis && this.redisClient) {
                // Use Upstash Redis REST API
                await this.redisClient.setex(`session:${sessionId}`, this.sessionTTL, JSON.stringify(data));
            } else {
                this.localCache.set(sessionId, data);
            }
        } catch (error) {
            console.error(`❌ Error setting session ${sessionId}:`, error);
            // Fallback to local cache if Redis fails
            this.localCache.set(sessionId, data);
        }
    }
    
    /**
     * Update session activity
     */
    async updateActivity(sessionId) {
        const timestamp = Date.now();
        
        // Update local tracking
        if (this.activeSessions.has(sessionId)) {
            const session = this.activeSessions.get(sessionId);
            session.lastActivity = timestamp;
            session.requestCount++;
        }
        
        // Update stored session data
        const sessionData = await this.getSessionData(sessionId);
        if (sessionData) {
            sessionData.lastActivity = timestamp;
            sessionData.requestCount++;
            await this.setSessionData(sessionId, sessionData);
        }
    }
    
    /**
     * Add message to conversation history
     */
    async addMessage(sessionId, role, content, metadata = {}) {
        const sessionData = await this.getSessionData(sessionId);
        if (!sessionData) {
            console.warn(`⚠️ Session ${sessionId} not found for message`);
            return false;
        }
        
        const message = {
            role,
            content,
            timestamp: Date.now(),
            ...metadata
        };
        
        sessionData.conversationHistory.push(message);
        
        // Keep only last 20 messages to prevent memory bloat
        if (sessionData.conversationHistory.length > 20) {
            sessionData.conversationHistory = sessionData.conversationHistory.slice(-20);
        }
        
        await this.setSessionData(sessionId, sessionData);
        await this.updateActivity(sessionId);
        
        return true;
    }
    
    /**
     * Get conversation history
     */
    async getConversationHistory(sessionId, limit = 10) {
        const sessionData = await this.getSessionData(sessionId);
        if (!sessionData || !sessionData.conversationHistory) {
            return [];
        }
        
        return sessionData.conversationHistory.slice(-limit);
    }
    
    /**
     * Record latency metrics
     */
    async recordLatency(sessionId, latencyData) {
        const sessionData = await this.getSessionData(sessionId);
        if (sessionData) {
            sessionData.totalLatency += latencyData.total || 0;
            await this.setSessionData(sessionId, sessionData);
        }
        
        // Update metrics
        if (!this.sessionMetrics.has(sessionId)) {
            this.sessionMetrics.set(sessionId, {
                requestCount: 0,
                totalLatency: 0,
                avgLatency: 0
            });
        }
        
        const metrics = this.sessionMetrics.get(sessionId);
        metrics.requestCount++;
        metrics.totalLatency += latencyData.total || 0;
        metrics.avgLatency = metrics.totalLatency / metrics.requestCount;
    }
    
    /**
     * Close session
     */
    async closeSession(sessionId) {
        try {
            // Update session status
            const sessionData = await this.getSessionData(sessionId);
            if (sessionData) {
                sessionData.status = 'closed';
                sessionData.closedAt = Date.now();
                await this.setSessionData(sessionId, sessionData);
            }
            
            // Remove from active tracking
            this.activeSessions.delete(sessionId);
            this.sessionMetrics.delete(sessionId);
            
            console.log(`🔒 Session closed: ${sessionId}`);
            return true;
        } catch (error) {
            console.error(`❌ Error closing session ${sessionId}:`, error);
            return false;
        }
    }
    
    /**
     * Get session statistics
     */
    getSessionStats() {
        const activeCount = this.activeSessions.size;
        const totalSessions = this.sessionMetrics.size;
        
        let totalRequests = 0;
        let totalLatency = 0;
        
        for (const metrics of this.sessionMetrics.values()) {
            totalRequests += metrics.requestCount;
            totalLatency += metrics.totalLatency;
        }
        
        return {
            activeSessions: activeCount,
            totalSessions,
            totalRequests,
            avgLatency: totalRequests > 0 ? totalLatency / totalRequests : 0,
            memoryUsage: process.memoryUsage()
        };
    }
    
    /**
     * Cleanup expired sessions
     */
    async cleanupExpiredSessions() {
        const now = Date.now();
        const expiredThreshold = 30 * 60 * 1000; // 30 minutes
        
        let cleanedCount = 0;
        
        for (const [sessionId, session] of this.activeSessions.entries()) {
            if (now - session.lastActivity > expiredThreshold) {
                await this.closeSession(sessionId);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            console.log(`🧹 Cleaned up ${cleanedCount} expired sessions`);
        }
        
        return cleanedCount;
    }
    
    /**
     * Validate session exists and is active
     */
    async isValidSession(sessionId) {
        if (!sessionId) return false;
        
        const sessionData = await this.getSessionData(sessionId);
        return sessionData && sessionData.status === 'active';
    }
}

module.exports = SessionManager;
