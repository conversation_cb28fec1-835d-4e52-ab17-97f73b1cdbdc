/**
 * Metrics Collector
 * Performance monitoring and statistics
 */

class MetricsCollector {
    constructor() {
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            avgSttLatency: 0,
            avgLlmLatency: 0,
            avgTtsLatency: 0,
            avgTotalLatency: 0,
            minLatency: Infinity,
            maxLatency: 0,
            requestsPerSecond: 0,
            startTime: Date.now()
        };
        
        this.recentRequests = [];
        this.maxRecentRequests = 1000;
        
        // Start periodic calculations
        this.startPeriodicCalculations();
        
        console.log('📊 Metrics collector initialized');
    }
    
    recordRequest(latencies) {
        const now = Date.now();
        
        this.stats.totalRequests++;
        this.stats.successfulRequests++;
        
        // Update latency averages
        const n = this.stats.successfulRequests;
        this.stats.avgSttLatency = this.updateAverage(this.stats.avgSttLatency, latencies.sttLatency, n);
        this.stats.avgLlmLatency = this.updateAverage(this.stats.avgLlmLatency, latencies.llmLatency, n);
        this.stats.avgTtsLatency = this.updateAverage(this.stats.avgTtsLatency, latencies.ttsLatency, n);
        this.stats.avgTotalLatency = this.updateAverage(this.stats.avgTotalLatency, latencies.totalLatency, n);
        
        // Update min/max
        this.stats.minLatency = Math.min(this.stats.minLatency, latencies.totalLatency);
        this.stats.maxLatency = Math.max(this.stats.maxLatency, latencies.totalLatency);
        
        // Store recent request for RPS calculation
        this.recentRequests.push({
            timestamp: now,
            latencies
        });
        
        // Limit recent requests array size
        if (this.recentRequests.length > this.maxRecentRequests) {
            this.recentRequests.shift();
        }
        
        console.log(`📊 Request recorded: STT=${latencies.sttLatency}ms, LLM=${latencies.llmLatency}ms, TTS=${latencies.ttsLatency}ms, Total=${latencies.totalLatency}ms`);
    }
    
    recordError() {
        this.stats.totalRequests++;
        this.stats.failedRequests++;
        
        console.log('📊 Error recorded');
    }
    
    updateAverage(currentAvg, newValue, count) {
        return (currentAvg * (count - 1) + newValue) / count;
    }
    
    calculateRequestsPerSecond() {
        const now = Date.now();
        const oneSecondAgo = now - 1000;
        
        const recentCount = this.recentRequests.filter(req => req.timestamp > oneSecondAgo).length;
        this.stats.requestsPerSecond = recentCount;
    }
    
    startPeriodicCalculations() {
        // Update RPS every second
        setInterval(() => {
            this.calculateRequestsPerSecond();
        }, 1000);
        
        console.log('📊 Periodic calculations started');
    }
    
    getStats() {
        const uptime = Date.now() - this.stats.startTime;
        const uptimeSeconds = Math.floor(uptime / 1000);
        
        return {
            ...this.stats,
            uptime: uptimeSeconds,
            uptimeFormatted: this.formatUptime(uptimeSeconds),
            successRate: this.stats.totalRequests > 0 ? 
                (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%',
            averageRps: this.stats.totalRequests > 0 ? 
                (this.stats.totalRequests / (uptimeSeconds || 1)).toFixed(2) : '0'
        };
    }
    
    getDetailedStats() {
        const basicStats = this.getStats();
        
        // Calculate percentiles from recent requests
        const recentLatencies = this.recentRequests.map(req => req.latencies.totalLatency).sort((a, b) => a - b);
        
        const percentiles = {};
        if (recentLatencies.length > 0) {
            percentiles.p50 = this.calculatePercentile(recentLatencies, 50);
            percentiles.p90 = this.calculatePercentile(recentLatencies, 90);
            percentiles.p95 = this.calculatePercentile(recentLatencies, 95);
            percentiles.p99 = this.calculatePercentile(recentLatencies, 99);
        }
        
        return {
            ...basicStats,
            percentiles,
            recentRequestsCount: this.recentRequests.length
        };
    }
    
    calculatePercentile(sortedArray, percentile) {
        if (sortedArray.length === 0) return 0;
        
        const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
        return sortedArray[Math.max(0, index)];
    }
    
    formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
    
    // Get performance insights
    getPerformanceInsights() {
        const stats = this.getDetailedStats();
        const insights = [];
        
        // Latency insights
        if (stats.avgTotalLatency > 2000) {
            insights.push({
                type: 'warning',
                message: 'Average total latency is high (>2s)',
                suggestion: 'Consider optimizing API calls or adding more workers'
            });
        }
        
        if (stats.avgSttLatency > 1000) {
            insights.push({
                type: 'warning',
                message: 'STT latency is high (>1s)',
                suggestion: 'Check Deepgram API performance or network connectivity'
            });
        }
        
        if (stats.avgLlmLatency > 1500) {
            insights.push({
                type: 'warning',
                message: 'LLM latency is high (>1.5s)',
                suggestion: 'Consider using a faster OpenAI model or reducing context length'
            });
        }
        
        if (stats.avgTtsLatency > 800) {
            insights.push({
                type: 'warning',
                message: 'TTS latency is high (>800ms)',
                suggestion: 'Check Piper TTS performance or increase cache size'
            });
        }
        
        // Success rate insights
        const successRate = parseFloat(stats.successRate);
        if (successRate < 95) {
            insights.push({
                type: 'error',
                message: `Success rate is low (${stats.successRate})`,
                suggestion: 'Check API keys, network connectivity, and error logs'
            });
        }
        
        // Performance insights
        if (stats.requestsPerSecond > 10) {
            insights.push({
                type: 'info',
                message: 'High request rate detected',
                suggestion: 'Monitor system resources and consider scaling'
            });
        }
        
        return insights;
    }
    
    // Reset statistics
    reset() {
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            avgSttLatency: 0,
            avgLlmLatency: 0,
            avgTtsLatency: 0,
            avgTotalLatency: 0,
            minLatency: Infinity,
            maxLatency: 0,
            requestsPerSecond: 0,
            startTime: Date.now()
        };
        
        this.recentRequests = [];
        
        console.log('📊 Metrics reset');
    }
    
    // Export metrics for external monitoring
    exportPrometheus() {
        const stats = this.getStats();
        
        return `
# HELP voice_chat_requests_total Total number of voice chat requests
# TYPE voice_chat_requests_total counter
voice_chat_requests_total ${stats.totalRequests}

# HELP voice_chat_requests_successful_total Total number of successful requests
# TYPE voice_chat_requests_successful_total counter
voice_chat_requests_successful_total ${stats.successfulRequests}

# HELP voice_chat_requests_failed_total Total number of failed requests
# TYPE voice_chat_requests_failed_total counter
voice_chat_requests_failed_total ${stats.failedRequests}

# HELP voice_chat_latency_stt_ms Average STT latency in milliseconds
# TYPE voice_chat_latency_stt_ms gauge
voice_chat_latency_stt_ms ${stats.avgSttLatency}

# HELP voice_chat_latency_llm_ms Average LLM latency in milliseconds
# TYPE voice_chat_latency_llm_ms gauge
voice_chat_latency_llm_ms ${stats.avgLlmLatency}

# HELP voice_chat_latency_tts_ms Average TTS latency in milliseconds
# TYPE voice_chat_latency_tts_ms gauge
voice_chat_latency_tts_ms ${stats.avgTtsLatency}

# HELP voice_chat_latency_total_ms Average total latency in milliseconds
# TYPE voice_chat_latency_total_ms gauge
voice_chat_latency_total_ms ${stats.avgTotalLatency}

# HELP voice_chat_requests_per_second Current requests per second
# TYPE voice_chat_requests_per_second gauge
voice_chat_requests_per_second ${stats.requestsPerSecond}
        `.trim();
    }
}

module.exports = MetricsCollector;
