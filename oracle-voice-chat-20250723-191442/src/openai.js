/**
 * Optimized OpenAI Client
 * High-performance chat completions with connection pooling
 */

const axios = require('axios');

class OpenAIClient {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://api.openai.com/v1/chat/completions';
        
        // Optimized HTTP client
        this.client = axios.create({
            timeout: 30000,
            maxRedirects: 0,
            httpAgent: new (require('http').Agent)({
                keepAlive: true,
                maxSockets: 10,
                maxFreeSockets: 5
            }),
            httpsAgent: new (require('https').Agent)({
                keepAlive: true,
                maxSockets: 10,
                maxFreeSockets: 5
            })
        });
        
        // System prompt optimized for speed and Slovak language
        this.systemPrompt = {
            role: 'system',
            content: 'Si užito<PERSON>ný asistent, ktorý odpovedá po slovensky. Buď stručný a priateľský. Odpovedaj v 1-2 vetách.'
        };
        
        console.log('🤖 OpenAI client initialized with connection pooling');
    }
    
    async chatCompletion(userMessage, conversationHistory = []) {
        const startTime = Date.now();
        
        try {
            // Build messages array
            const messages = [this.systemPrompt];
            
            // Add recent conversation history (limit to last 10 messages for speed)
            const recentHistory = conversationHistory.slice(-10);
            messages.push(...recentHistory.map(msg => ({
                role: msg.role,
                content: msg.content
            })));
            
            // Add current user message
            messages.push({
                role: 'user',
                content: userMessage
            });
            
            const requestData = {
                model: 'gpt-3.5-turbo', // Fast model for low latency
                messages,
                max_tokens: 150, // Limit for speed
                temperature: 0.7,
                stream: false
            };
            
            const response = await this.client.post(this.baseUrl, requestData, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const aiResponse = response.data?.choices?.[0]?.message?.content || 
                'Prepáčte, nemôžem teraz odpovedať.';
            
            const duration = Date.now() - startTime;
            console.log(`🤖 OpenAI completion: ${duration}ms - "${aiResponse}"`);
            
            // Log token usage for monitoring
            if (response.data?.usage) {
                const usage = response.data.usage;
                console.log(`📊 Token usage: ${usage.prompt_tokens} + ${usage.completion_tokens} = ${usage.total_tokens}`);
            }
            
            return aiResponse;
            
        } catch (error) {
            console.error('OpenAI error:', error.message);
            
            if (error.response) {
                const status = error.response.status;
                const data = error.response.data;
                
                if (status === 429) {
                    throw new Error('OpenAI rate limit exceeded');
                } else if (status === 401) {
                    throw new Error('OpenAI authentication failed');
                } else {
                    throw new Error(`OpenAI API error ${status}: ${data?.error?.message || 'Unknown error'}`);
                }
            } else if (error.request) {
                throw new Error('OpenAI network error');
            } else {
                throw new Error(`OpenAI client error: ${error.message}`);
            }
        }
    }
    
    async chatCompletionStreaming(userMessage, conversationHistory = []) {
        // TODO: Implement streaming chat completion
        const { Readable } = require('stream');
        
        return new Promise((resolve, reject) => {
            const stream = new Readable({
                read() {}
            });
            
            // For now, just return the complete response as a stream
            this.chatCompletion(userMessage, conversationHistory)
                .then(response => {
                    stream.push(response);
                    stream.push(null); // End stream
                    resolve(stream);
                })
                .catch(reject);
        });
    }
    
    // Health check
    async healthCheck() {
        try {
            await this.chatCompletion('Test', []);
            return true;
        } catch (error) {
            console.error('OpenAI health check failed:', error.message);
            return false;
        }
    }
    
    // Get client statistics
    getStats() {
        return {
            baseUrl: this.baseUrl,
            model: 'gpt-3.5-turbo',
            hasApiKey: !!this.apiKey,
            systemPrompt: this.systemPrompt.content
        };
    }
    
    // Update model for different use cases
    setModel(model) {
        this.model = model;
        console.log(`🤖 OpenAI model changed to: ${model}`);
    }
    
    // Update system prompt
    setSystemPrompt(prompt) {
        this.systemPrompt.content = prompt;
        console.log('🤖 OpenAI system prompt updated');
    }
}

module.exports = OpenAIClient;
