#!/bin/bash
set -e

echo "🔧 Installing Fast Voice Server on Oracle Cloud..."

# Update system
sudo apt update

# Install Node.js 18 if not present
if ! command -v node &> /dev/null || [[ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt 18 ]]; then
    echo "📦 Installing Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install PM2 globally
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    sudo npm install -g pm2
fi

# Install Piper TTS if not present
if ! command -v piper &> /dev/null; then
    echo "🔊 Installing Piper TTS..."
    
    # Create piper directory
    mkdir -p /home/<USER>/piper_env/bin
    cd /home/<USER>/piper_env/bin
    
    # Download Piper binary
    wget -O piper.tar.gz https://github.com/rhasspy/piper/releases/download/v1.2.0/piper_linux_x86_64.tar.gz
    tar -xzf piper.tar.gz
    mv piper/piper .
    chmod +x piper
    rm -rf piper piper.tar.gz
    
    # Download Slovak model
    mkdir -p /home/<USER>/models
    cd /home/<USER>/models
    wget -O sk_SK-lili-medium.onnx https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/sk/sk_SK/lili/medium/sk_SK-lili-medium.onnx
    wget -O sk_SK-lili-medium.onnx.json https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/sk/sk_SK/lili/medium/sk_SK-lili-medium.onnx.json
fi

# Configure firewall
echo "🔥 Configuring firewall..."
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5001/tcp
sudo ufw --force enable

# Stop existing services
echo "🛑 Stopping existing services..."
sudo systemctl stop fast-voice-server-node 2>/dev/null || true
pm2 stop fast-voice-server 2>/dev/null || true
pm2 delete fast-voice-server 2>/dev/null || true

# Install dependencies
echo "📦 Installing production dependencies..."
npm install --production

# Create log directory
sudo mkdir -p /var/log/fast-voice-server
sudo chown ubuntu:ubuntu /var/log/fast-voice-server

# Install systemd service (backup option)
sudo cp fast-voice-server.service /etc/systemd/system/fast-voice-server-node.service
sudo systemctl daemon-reload
sudo systemctl enable fast-voice-server-node

# Start with PM2 (preferred)
echo "🚀 Starting with PM2..."
pm2 start ecosystem.config.js
pm2 save
pm2 startup | tail -1 | sudo bash || true

echo "✅ Fast Voice Server installed and started!"
echo ""
echo "📊 Status commands:"
echo "  pm2 status"
echo "  pm2 logs fast-voice-server"
echo "  pm2 monit"
echo ""
echo "🔄 Control commands:"
echo "  pm2 restart fast-voice-server"
echo "  pm2 stop fast-voice-server"
echo "  pm2 start fast-voice-server"
echo ""
echo "🌐 Test endpoints:"
echo "  curl http://*************:80/health"
echo "  curl http://*************:80/api/piper-test"
echo ""
echo "🎯 Ready for Cloudflare frontend connection!"
