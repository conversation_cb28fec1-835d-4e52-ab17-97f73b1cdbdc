#!/usr/bin/env node

/**
 * Test script for Session Management functionality
 */

require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testSessionManagement() {
    console.log('🧪 Testing Session Management...');
    
    try {
        // Test Redis health endpoint
        console.log('\n1. Testing Redis health check...');
        const healthResponse = await axios.get(`${BASE_URL}/api/redis/health`);
        console.log('✅ Redis health:', healthResponse.data);
        
        // Test session stats
        console.log('\n2. Testing session stats...');
        const statsResponse = await axios.get(`${BASE_URL}/api/sessions`);
        console.log('✅ Session stats:', statsResponse.data);
        
        // Create a test session by making a voice chat request
        console.log('\n3. Creating test session...');
        const FormData = require('form-data');
        const fs = require('fs');
        
        // Create a dummy audio file for testing
        const dummyAudio = Buffer.from('dummy audio data');
        const form = new FormData();
        form.append('audio', dummyAudio, {
            filename: 'test.wav',
            contentType: 'audio/wav'
        });
        form.append('session_id', 'test-session-123');
        form.append('message', 'Hello, this is a test message');
        
        try {
            const voiceChatResponse = await axios.post(`${BASE_URL}/api/voice-chat`, form, {
                headers: {
                    ...form.getHeaders(),
                },
                timeout: 10000
            });
            console.log('✅ Voice chat response received');
            
            // Test getting specific session data
            console.log('\n4. Testing session data retrieval...');
            const sessionResponse = await axios.get(`${BASE_URL}/api/sessions/test-session-123`);
            console.log('✅ Session data:', {
                id: sessionResponse.data.id,
                requestCount: sessionResponse.data.requestCount,
                status: sessionResponse.data.status,
                conversationLength: sessionResponse.data.conversationHistory?.length || 0
            });
            
        } catch (voiceError) {
            console.log('⚠️ Voice chat test skipped (expected if no audio processing):', voiceError.response?.status);
        }
        
        // Test session cleanup
        console.log('\n5. Testing session cleanup...');
        const cleanupResponse = await axios.post(`${BASE_URL}/api/sessions/cleanup`);
        console.log('✅ Cleanup result:', cleanupResponse.data);
        
        // Test updated session stats
        console.log('\n6. Testing updated session stats...');
        const finalStatsResponse = await axios.get(`${BASE_URL}/api/sessions`);
        console.log('✅ Final session stats:', finalStatsResponse.data);
        
        console.log('\n🎉 Session management tests completed successfully!');
        
    } catch (error) {
        console.error('❌ Session management test failed:', error.response?.data || error.message);
        process.exit(1);
    }
}

// Check if server is running
async function checkServer() {
    try {
        await axios.get(`${BASE_URL}/health`);
        console.log('✅ Server is running');
        return true;
    } catch (error) {
        console.log('❌ Server is not running. Please start the server first:');
        console.log('   npm start');
        return false;
    }
}

// Run the test
async function main() {
    const serverRunning = await checkServer();
    if (serverRunning) {
        await testSessionManagement();
    }
}

main();
