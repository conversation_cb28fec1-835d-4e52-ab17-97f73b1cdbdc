{"name": "fast-voice-server-oracle", "version": "1.0.0", "description": "Ultra-fast voice chat server for Oracle Cloud + Cloudflare", "main": "server.js", "scripts": {"start": "node server.js", "pm2": "pm2 start ecosystem.config.js", "stop": "pm2 stop fast-voice-server", "restart": "pm2 restart fast-voice-server", "logs": "pm2 logs fast-voice-server"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "ws": "^8.14.2", "axios": "^1.6.2", "form-data": "^4.0.0", "uuid": "^9.0.1", "compression": "^1.7.4", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "node-cache": "^5.1.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}