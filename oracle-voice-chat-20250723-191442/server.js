#!/usr/bin/env node

/**
 * Fast Voice Server - Ultra-Low Latency Voice Chat
 * Optimized Node.js implementation with parallel processing
 */

// Load environment variables
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const multer = require('multer');
const WebSocket = require('ws');
const http = require('http');
const cluster = require('cluster');
const os = require('os');
const compression = require('compression');
const helmet = require('helmet');
const morgan = require('morgan');
const NodeCache = require('node-cache');
const { v4: uuidv4 } = require('uuid');

// Import optimized modules
const DeepgramClient = require('./src/deepgram');
const OpenAIClient = require('./src/openai');
const TTSEngine = require('./src/tts');
const AudioProcessor = require('./src/audio');
const MetricsCollector = require('./src/metrics');
const SessionManager = require('./src/session-manager');
const WebSocketHandler = require('./src/websocket-handler');

// Configuration for Oracle + Cloudflare deployment
const config = {
    port: process.env.PORT || 80,
    wsPort: process.env.WS_PORT || 5001,
    workers: process.env.WORKERS || os.cpus().length,
    deepgramApiKey: process.env.DEEPGRAM_API_KEY || '****************************************',
    openaiApiKey: process.env.OPENAI_API_KEY || '***************************************************',
    piperPath: process.env.PIPER_PATH || null, // Let TTSEngine auto-detect
    modelPath: process.env.MODEL_PATH || null, // Let TTSEngine auto-detect
    maxConcurrentRequests: process.env.MAX_CONCURRENT || 100,
    cacheSize: process.env.CACHE_SIZE || 1000,
    enableClustering: process.env.ENABLE_CLUSTERING !== 'false'
};

// Cluster setup for maximum performance
if (config.enableClustering && cluster.isMaster) {
    console.log(`🚀 Starting Fast Voice Server with ${config.workers} workers`);
    
    // Fork workers
    for (let i = 0; i < config.workers; i++) {
        cluster.fork();
    }
    
    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died. Restarting...`);
        cluster.fork();
    });
    
    return;
}

// Worker process
console.log(`🔧 Worker ${process.pid} starting...`);

// Initialize components
const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Enhanced CORS middleware for Cloudflare Pages + Oracle backend
app.use((req, res, next) => {
    // Set comprehensive CORS headers for Cloudflare frontend
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Content-Disposition, X-Forwarded-For, X-Real-IP');
    res.setHeader('Access-Control-Allow-Credentials', 'false');
    res.setHeader('Access-Control-Max-Age', '86400');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Length, Content-Type, Content-Disposition');

    console.log(`🌐 CORS request: ${req.method} ${req.url} from ${req.headers.origin || 'unknown'} (${req.ip})`);

    // Handle preflight OPTIONS requests
    if (req.method === 'OPTIONS') {
        console.log(`✅ CORS preflight handled for ${req.url}`);
        return res.status(204).end();
    }

    next();
});

// Initialize optimized clients
const deepgram = new DeepgramClient(config.deepgramApiKey);
const openai = new OpenAIClient(config.openaiApiKey);
const tts = new TTSEngine(config.piperPath, config.modelPath);
const audioProcessor = new AudioProcessor();
const metrics = new MetricsCollector();

// Initialize enhanced session management
const sessionManager = new SessionManager({
    useRedis: process.env.USE_REDIS === 'true',
    sessionTTL: 3600 // 1 hour
});

// Initialize WebSocket handler
const wsHandler = new WebSocketHandler(sessionManager, deepgram, openai, tts);

// Caches for performance
const conversationCache = new NodeCache({ stdTTL: 3600 }); // 1 hour
const ttsCache = new NodeCache({ stdTTL: 7200, maxKeys: config.cacheSize }); // 2 hours

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '50mb' }));

// Multer for file uploads
const upload = multer({
    storage: multer.memoryStorage(),
    limits: { fileSize: 50 * 1024 * 1024 }, // 50MB
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('audio/')) {
            cb(null, true);
        } else {
            cb(new Error('Only audio files allowed'));
        }
    }
});

// Mock audio endpoint for development
app.get('/mock-audio/:filename', (req, res) => {
    // Return a simple JSON response for mock audio
    res.json({
        message: 'Mock audio file (Oracle Piper nedostupný)',
        filename: req.params.filename,
        url: `/mock-audio/${req.params.filename}`,
        type: 'mock',
        note: 'Nasaďte na Oracle server pre skutočný Piper TTS'
    });
});

// Piper TTS status endpoint (auto-detection)
app.get('/api/piper-status', (req, res) => {
    const TTSEngine = require('./src/tts');

    // Create temporary TTSEngine instance to get detected paths
    const tempEngine = new TTSEngine(null, null);

    let piperAvailable = false;
    let modelAvailable = false;

    // Test Piper executable
    try {
        if (tempEngine.piperPath === 'piper') {
            // Test system PATH piper
            require('child_process').execSync('piper --help', { stdio: 'ignore' });
            piperAvailable = true;
        } else {
            // Test specific path
            const fs = require('fs');
            piperAvailable = fs.existsSync(tempEngine.piperPath);
        }
    } catch (e) {
        piperAvailable = false;
    }

    // Test model file
    try {
        const fs = require('fs');
        modelAvailable = fs.existsSync(tempEngine.modelPath);
    } catch (e) {
        modelAvailable = false;
    }

    const isLocal = tempEngine.piperPath === 'piper' || tempEngine.piperPath.includes('/Users/');
    const isOracle = tempEngine.piperPath.includes('/home/<USER>/');

    res.json({
        piper_available: piperAvailable,
        model_available: modelAvailable,
        piper_path: tempEngine.piperPath,
        model_path: tempEngine.modelPath,
        status: piperAvailable && modelAvailable ? 'ready' : 'not_available',
        environment: isLocal ? 'local_development' : (isOracle ? 'oracle_server' : 'unknown'),
        message: piperAvailable && modelAvailable ?
            (isLocal ? 'Lokálny Piper TTS je dostupný' : 'Oracle Piper TTS je dostupný') :
            'Piper TTS nie je dostupný'
    });
});

// Request tracking for concurrency control
let activeRequests = 0;
const requestQueue = [];

// Middleware for request limiting
const requestLimiter = (req, res, next) => {
    if (activeRequests >= config.maxConcurrentRequests) {
        return res.status(503).json({ 
            error: 'Server busy', 
            message: 'Too many concurrent requests' 
        });
    }
    
    activeRequests++;
    res.on('finish', () => {
        activeRequests--;
        processQueue();
    });
    
    next();
};

const processQueue = () => {
    if (requestQueue.length > 0 && activeRequests < config.maxConcurrentRequests) {
        const nextRequest = requestQueue.shift();
        nextRequest();
    }
};

// Health check endpoint
app.get('/health', (req, res) => {
    res.send('OK - Fast Voice Chat Server with Node.js is running!');
});

// Metrics endpoint with enhanced session stats
app.get('/api/metrics', (req, res) => {
    const stats = metrics.getStats();
    const sessionStats = sessionManager.getSessionStats();
    const connectionStats = wsHandler.getConnectionStats();

    res.json({
        ...stats,
        activeRequests,
        queueLength: requestQueue.length,
        sessions: sessionStats,
        connections: connectionStats,
        cacheStats: {
            conversations: conversationCache.getStats(),
            tts: ttsCache.getStats()
        },
        system: {
            memory: process.memoryUsage(),
            uptime: process.uptime(),
            pid: process.pid
        }
    });
});

// Session management endpoints
app.get('/api/sessions', async (req, res) => {
    try {
        const stats = sessionManager.getSessionStats();
        res.json(stats);
    } catch (error) {
        res.status(500).json({ error: 'Failed to get session stats' });
    }
});

app.get('/api/sessions/:sessionId', async (req, res) => {
    try {
        const sessionData = await sessionManager.getSessionData(req.params.sessionId);
        if (!sessionData) {
            return res.status(404).json({ error: 'Session not found' });
        }
        res.json(sessionData);
    } catch (error) {
        res.status(500).json({ error: 'Failed to get session data' });
    }
});

app.delete('/api/sessions/:sessionId', async (req, res) => {
    try {
        const success = await sessionManager.closeSession(req.params.sessionId);
        if (success) {
            res.json({ message: 'Session closed successfully' });
        } else {
            res.status(404).json({ error: 'Session not found' });
        }
    } catch (error) {
        res.status(500).json({ error: 'Failed to close session' });
    }
});

// Redis health check endpoint
app.get('/api/redis/health', async (req, res) => {
    try {
        const isRedisEnabled = sessionManager.useRedis;
        const hasRedisClient = sessionManager.redisClient !== null;

        let redisStatus = 'disabled';
        let connectionTest = null;

        if (isRedisEnabled && hasRedisClient) {
            try {
                // Test Redis connection with a simple ping-like operation
                const testKey = `health:${Date.now()}`;
                await sessionManager.redisClient.setex(testKey, 5, 'ping');
                const result = await sessionManager.redisClient.get(testKey);
                await sessionManager.redisClient.del(testKey);

                redisStatus = result === 'ping' ? 'connected' : 'error';
                connectionTest = 'success';
            } catch (error) {
                redisStatus = 'error';
                connectionTest = error.message;
            }
        }

        res.json({
            redis_enabled: isRedisEnabled,
            redis_client: hasRedisClient,
            redis_status: redisStatus,
            connection_test: connectionTest,
            fallback_cache: !isRedisEnabled || !hasRedisClient ? 'active' : 'standby'
        });
    } catch (error) {
        res.status(500).json({ error: 'Failed to check Redis health' });
    }
});

// Session cleanup endpoint
app.post('/api/sessions/cleanup', async (req, res) => {
    try {
        const cleanedCount = await sessionManager.cleanupExpiredSessions();
        res.json({
            message: 'Session cleanup completed',
            cleaned_sessions: cleanedCount
        });
    } catch (error) {
        res.status(500).json({ error: 'Failed to cleanup sessions' });
    }
});

// Main voice chat endpoint - optimized pipeline
app.post('/api/voice-chat', requestLimiter, upload.single('audio'), async (req, res) => {
    const startTime = Date.now();
    const sessionId = req.body.session_id || uuidv4();
    
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No audio file provided' });
        }
        
        console.log(`🎤 Processing voice chat for session ${sessionId}`);
        
        // Process audio in optimized pipeline
        const result = await processVoicePipeline(req.file.buffer, sessionId);
        
        const totalLatency = Date.now() - startTime;
        
        // Update metrics
        metrics.recordRequest({
            sttLatency: result.latency.stt,
            llmLatency: result.latency.llm,
            ttsLatency: result.latency.tts,
            totalLatency
        });
        
        res.json({
            ...result,
            session_id: sessionId,
            latency: {
                ...result.latency,
                total_ms: totalLatency
            }
        });
        
        console.log(`✅ Voice chat completed in ${totalLatency}ms`);
        
    } catch (error) {
        console.error('Voice chat error:', error);
        metrics.recordError();
        res.status(500).json({ 
            error: 'Processing failed', 
            message: error.message 
        });
    }
});

// Enhanced voice processing pipeline with speech detection
async function processVoicePipeline(audioBuffer, sessionId) {
    const timings = {};
    const pipelineStart = Date.now();

    console.log(`🎤 Starting voice pipeline for session ${sessionId} (${audioBuffer.length} bytes)`);

    // Step 1: Enhanced audio processing and speech detection
    const audioStart = Date.now();
    let processedAudio;
    try {
        processedAudio = await audioProcessor.process(audioBuffer);
        console.log(`🎵 Audio processing completed: ${audioBuffer.length} → ${processedAudio.length} bytes`);
    } catch (error) {
        console.error('Audio processing failed:', error.message);
        throw new Error(`Audio processing failed: ${error.message}`);
    }
    timings.audio = Date.now() - audioStart;

    // Step 2: Speech-to-Text with enhanced error handling
    const sttStart = Date.now();
    let transcript;
    try {
        transcript = await deepgram.transcribe(processedAudio);

        if (!transcript || transcript.trim().length === 0) {
            throw new Error('No speech detected in audio');
        }

        console.log(`🎤 STT completed: "${transcript}"`);
    } catch (error) {
        console.error('STT failed:', error.message);

        // Try with original audio if processed audio failed
        if (processedAudio !== audioBuffer) {
            console.log('🔄 Retrying STT with original audio...');
            try {
                transcript = await deepgram.transcribe(audioBuffer);
                if (!transcript || transcript.trim().length === 0) {
                    throw new Error('No speech detected in original audio either');
                }
                console.log(`🎤 STT retry successful: "${transcript}"`);
            } catch (retryError) {
                throw new Error(`STT failed on both processed and original audio: ${error.message}`);
            }
        } else {
            throw error;
        }
    }
    timings.stt = Date.now() - sttStart;

    // Step 3: Get conversation history and LLM response
    const llmStart = Date.now();
    const conversationHistory = getConversationHistory(sessionId);
    let aiResponse;
    try {
        aiResponse = await openai.chatCompletion(transcript, conversationHistory);
        console.log(`🤖 LLM completed: "${aiResponse}"`);
    } catch (error) {
        console.error('LLM failed:', error.message);
        throw new Error(`LLM processing failed: ${error.message}`);
    }
    timings.llm = Date.now() - llmStart;

    // Step 4: Text-to-Speech (with caching)
    const ttsStart = Date.now();
    let audioUrl;
    try {
        audioUrl = await generateTTSWithCache(aiResponse);
        console.log(`🔊 TTS completed: ${audioUrl}`);
    } catch (error) {
        console.error('TTS failed:', error.message);
        // TTS failure is not critical, continue without audio
        audioUrl = null;
    }
    timings.tts = Date.now() - ttsStart;

    // Step 5: Update conversation history
    updateConversationHistory(sessionId, transcript, aiResponse);

    const totalTime = Date.now() - pipelineStart;
    console.log(`✅ Voice pipeline completed in ${totalTime}ms (audio: ${timings.audio}ms, stt: ${timings.stt}ms, llm: ${timings.llm}ms, tts: ${timings.tts}ms)`);

    return {
        transcript,
        response: aiResponse,
        audio_url: audioUrl,
        latency: {
            audio_ms: timings.audio,
            stt_ms: timings.stt,
            llm_ms: timings.llm,
            tts_ms: timings.tts,
            total_ms: totalTime
        }
    };
}

// Conversation history management
function getConversationHistory(sessionId) {
    return conversationCache.get(sessionId) || [];
}

function updateConversationHistory(sessionId, userMessage, aiResponse) {
    let history = getConversationHistory(sessionId);
    
    const timestamp = Date.now();
    history.push(
        { role: 'user', content: userMessage, timestamp },
        { role: 'assistant', content: aiResponse, timestamp }
    );
    
    // Keep only last 20 messages
    if (history.length > 20) {
        history = history.slice(-20);
    }
    
    conversationCache.set(sessionId, history);
}

// TTS with caching
async function generateTTSWithCache(text) {
    const cacheKey = Buffer.from(text).toString('base64').substring(0, 32);
    
    let audioPath = ttsCache.get(cacheKey);
    if (audioPath && require('fs').existsSync(audioPath)) {
        console.log('🎵 TTS cache hit');
        return audioPath;
    }
    
    audioPath = await tts.synthesize(text);
    ttsCache.set(cacheKey, audioPath);
    
    return audioPath;
}

// Individual API endpoints for testing
app.post('/api/transcribe', requestLimiter, upload.single('audio'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No audio file provided' });
        }
        
        const transcript = await deepgram.transcribe(req.file.buffer);
        res.json({ transcript, status: 'success' });
        
    } catch (error) {
        console.error('Transcribe error:', error);
        res.status(500).json({ error: 'Transcription failed' });
    }
});

app.post('/api/chat', requestLimiter, async (req, res) => {
    try {
        const { message } = req.body;
        if (!message) {
            return res.status(400).json({ error: 'No message provided' });
        }
        
        const response = await openai.chatCompletion(message, []);
        res.json({ response, status: 'success' });
        
    } catch (error) {
        console.error('Chat error:', error);
        res.status(500).json({ error: 'Chat failed' });
    }
});

app.post('/api/speak', requestLimiter, async (req, res) => {
    try {
        const { text } = req.body;
        if (!text) {
            return res.status(400).json({ error: 'No text provided' });
        }

        const audioPath = await generateTTSWithCache(text);

        // Check if it's a mock URL (development mode)
        if (audioPath && audioPath.startsWith('/mock-audio/')) {
            // Return JSON response for mock audio
            res.json({
                message: 'Mock TTS audio generated',
                text: text,
                audio_url: audioPath,
                type: 'mock'
            });
        } else if (audioPath && require('fs').existsSync(audioPath)) {
            // Send real audio file with proper headers (production mode)
            const fs = require('fs');
            const stats = fs.statSync(audioPath);

            res.set({
                'Content-Type': 'audio/wav',
                'Content-Length': stats.size,
                'Cache-Control': 'no-store',
                'Accept-Ranges': 'bytes'
            });

            console.log(`🔊 Sending WAV file: ${audioPath} (${stats.size} bytes)`);
            res.sendFile(audioPath, { root: '/' });
        } else {
            // No audio generated
            res.status(404).json({
                error: 'Audio file not found',
                audio_url: audioPath
            });
        }

    } catch (error) {
        console.error('TTS error:', error);
        res.status(500).json({ error: 'TTS failed' });
    }
});

// WebSocket for real-time streaming with enhanced session management
wss.on('connection', async (ws, req) => {
    try {
        await wsHandler.handleConnection(ws, req);
    } catch (error) {
        console.error('❌ WebSocket connection error:', error);
        ws.close(1011, 'Connection setup failed');
    }
});

// Store active Deepgram connections per session
const deepgramConnections = new Map();

// Export handleAudioStream globally for WebSocket handler
global.handleAudioStream = handleAudioStream;

async function handleAudioStream(ws, audioData, sessionId) {
    try {
        // Generate unique request ID for this audio stream
        const requestId = `${sessionId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        console.log(`📦 Received audio stream: ${audioData.length} bytes for session ${sessionId} (requestId: ${requestId})`);

        // Use HTTP Deepgram API instead of WebSocket for better WebM support
        console.log(`🎤 Using HTTP Deepgram API for session ${sessionId}`);

        try {
            // Send status update
            ws.send(JSON.stringify({
                type: 'status',
                message: 'Rozpoznávam reč cez Deepgram...',
                session_id: sessionId
            }));

            // Use HTTP Deepgram transcription
            const transcript = await deepgram.transcribe(audioData);

            if (transcript && transcript.trim().length > 0) {
                console.log(`📝 Deepgram HTTP transcript: "${transcript}"`);

                // Send transcript to client
                ws.send(JSON.stringify({
                    type: 'transcript',
                    transcript: transcript,
                    is_final: true,
                    session_id: sessionId
                }));

                // Process full conversation
                await processFullConversation(ws, transcript, sessionId);

            } else {
                throw new Error('Nepodarilo sa rozpoznať reč alebo je príliš krátka. Skúste to znovu.');
            }

        } catch (error) {
            console.error(`❌ Deepgram HTTP error for session ${sessionId}:`, error);
            console.error(`❌ Error details:`, {
                message: error.message,
                stack: error.stack,
                name: error.name,
                code: error.code,
                audioSize: audioData.length,
                sessionId: sessionId
            });

            ws.send(JSON.stringify({
                type: 'error',
                message: error.message || 'Deepgram transcription failed',
                details: {
                    errorName: error.name,
                    errorCode: error.code,
                    audioSize: audioData.length,
                    sessionId: sessionId,
                    fullError: error.toString()
                }
            }));
        }

        return; // Skip WebSocket logic below

        // OLD WebSocket logic (kept as fallback)
        let deepgramWs = deepgramConnections.get(sessionId);

        // Always check if connection is healthy
        if (false && (!deepgramWs || deepgramWs.readyState !== WebSocket.OPEN)) {
            console.log(`🔌 Creating persistent Deepgram connection for session ${sessionId}`);

            // Cleanup old connection if exists
            if (deepgramWs) {
                console.log(`🧹 Cleaning up old Deepgram connection for session ${sessionId}`);
                deepgramWs.close();
                deepgramConnections.delete(sessionId);
            }

            // Create persistent Deepgram WebSocket connection with WebM support
            const deepgramUrl = `wss://api.deepgram.com/v1/listen?model=nova-2&language=sk&smart_format=true&interim_results=true&endpointing=true&utterance_end_ms=1000&vad_events=true&keep_alive=true&encoding=webm&sample_rate=48000&channels=1`;
            deepgramWs = new WebSocket(deepgramUrl, {
                headers: {
                    'Authorization': `Token ${config.deepgramApiKey}`
                }
            });

            // Set up keep-alive interval to prevent timeout
            const keepAliveInterval = setInterval(() => {
                if (deepgramWs && deepgramWs.readyState === WebSocket.OPEN) {
                    // Send keep-alive message (empty JSON)
                    deepgramWs.send(JSON.stringify({ type: 'KeepAlive' }));
                    console.log(`💓 Keep-alive sent for session ${sessionId}`);
                } else {
                    clearInterval(keepAliveInterval);
                }
            }, 8000); // Every 8 seconds to prevent 10s timeout

            // Store keep-alive interval for cleanup
            deepgramWs.keepAliveInterval = keepAliveInterval;

            // Set up connection event handlers
            deepgramWs.on('open', () => {
                console.log(`✅ Deepgram persistent connection established for session ${sessionId}`);

                // Send audio immediately after connection
                console.log(`🚀 Sending audio to persistent stream (${audioData.length} bytes)`);
                if (deepgramWs.readyState === WebSocket.OPEN) {
                    deepgramWs.send(audioData);
                    console.log(`📡 Audio sent to persistent stream`);
                    // Set current request ID for this audio stream
                    deepgramWs.currentRequestId = requestId;
                    console.log(`🆔 Set currentRequestId: ${requestId}`);
                }
            });

        // Store variables in WebSocket object for persistence across reuse
        deepgramWs.lastInterimTranscript = '';
        deepgramWs.finalTranscriptTimeout = null;
        deepgramWs.finalTranscriptReceived = false; // Flag to prevent timeout after final transcript
        deepgramWs.processingInProgress = false; // Flag to prevent duplicate processing
        deepgramWs.lastProcessedTranscript = ''; // Track last processed transcript to avoid duplicates
        deepgramWs.currentRequestId = null; // Track current audio request ID
        deepgramWs.processedRequestIds = new Set(); // Track processed request IDs to prevent duplicates

        deepgramWs.on('message', async (data) => {
                try {
                    const result = JSON.parse(data);

                    // Handle VAD (Voice Activity Detection) events
                    if (result.type === 'SpeechStarted') {
                        console.log(`🎤 Deepgram: Speech started for session ${sessionId}`);
                        // Reset flags for new utterance
                        deepgramWs.finalTranscriptReceived = false;
                        deepgramWs.processingInProgress = false;
                        deepgramWs.lastProcessedTranscript = '';
                        console.log(`🔄 Reset flags for new utterance (finalTranscriptReceived, processingInProgress, lastProcessedTranscript)`);
                        return;
                    }

                    if (result.type === 'UtteranceEnd') {
                        console.log(`🔚 Deepgram: Utterance ended for session ${sessionId}`);
                        return;
                    }

                    if (result.channel && result.channel.alternatives && result.channel.alternatives.length > 0) {
                        const transcript = result.channel.alternatives[0].transcript;
                        const isFinal = result.is_final;

                        if (transcript && transcript.trim().length > 0) {
                            console.log(`📝 Deepgram transcript (${isFinal ? 'final' : 'interim'}): "${transcript}"`);

                            // Send transcript to client
                            ws.send(JSON.stringify({
                                type: 'transcript',
                                transcript: transcript,
                                is_final: isFinal,
                                session_id: sessionId
                            }));

                            if (isFinal) {
                                // ANTI-DUPLICATE PROTECTION: Check if this request was already processed
                                if (!deepgramWs.currentRequestId) {
                                    console.log(`🚫 No currentRequestId - ignoring final transcript: "${transcript}"`);
                                    return;
                                }

                                if (deepgramWs.processedRequestIds.has(deepgramWs.currentRequestId)) {
                                    console.log(`🚫 Duplicate final transcript ignored (requestId already processed): "${transcript}" (requestId: ${deepgramWs.currentRequestId})`);
                                    return;
                                }

                                if (deepgramWs.processingInProgress) {
                                    console.log(`🚫 Duplicate final transcript ignored (processing in progress): "${transcript}" (requestId: ${deepgramWs.currentRequestId})`);
                                    return;
                                }

                                // Clear timeout and process final transcript
                                if (deepgramWs.finalTranscriptTimeout) {
                                    clearTimeout(deepgramWs.finalTranscriptTimeout);
                                    deepgramWs.finalTranscriptTimeout = null;
                                }

                                // Mark processing state
                                deepgramWs.processingInProgress = true;
                                deepgramWs.finalTranscriptReceived = true;
                                deepgramWs.processedRequestIds.add(deepgramWs.currentRequestId);
                                deepgramWs.lastProcessedTranscript = transcript;

                                console.log(`🤖 Processing final transcript: "${transcript}" (requestId: ${deepgramWs.currentRequestId})`);

                                try {
                                    await processFullConversation(ws, transcript, sessionId);
                                } finally {
                                    // Always reset processing flag
                                    deepgramWs.processingInProgress = false;
                                    console.log(`✅ Processing completed for: "${transcript}" (requestId: ${deepgramWs.currentRequestId})`);
                                }
                            } else {
                                // Only process interim if we haven't received final transcript yet
                                if (!deepgramWs.finalTranscriptReceived) {
                                    // Store interim transcript and set timeout
                                    deepgramWs.lastInterimTranscript = transcript;

                                    // Clear existing timeout
                                    if (deepgramWs.finalTranscriptTimeout) {
                                        clearTimeout(deepgramWs.finalTranscriptTimeout);
                                    }

                                    // Set timeout to process interim as final after 2 seconds
                                    const timeoutRequestId = deepgramWs.currentRequestId; // Capture current request ID for timeout
                                    deepgramWs.finalTranscriptTimeout = setTimeout(async () => {
                                    // ANTI-DUPLICATE PROTECTION: Check if this request was already processed
                                    if (!timeoutRequestId || deepgramWs.processedRequestIds.has(timeoutRequestId)) {
                                        console.log(`🚫 Timeout ignored - requestId already processed: ${timeoutRequestId}`);
                                        return;
                                    }

                                    if (deepgramWs.processingInProgress) {
                                        console.log(`🚫 Timeout ignored - processing already in progress (requestId: ${timeoutRequestId})`);
                                        return;
                                    }

                                    console.log(`⏰ Timeout: Processing interim transcript as final: "${deepgramWs.lastInterimTranscript}" (requestId: ${timeoutRequestId})`);

                                    // Mark processing state
                                    deepgramWs.processingInProgress = true;
                                    deepgramWs.processedRequestIds.add(timeoutRequestId);
                                    deepgramWs.lastProcessedTranscript = deepgramWs.lastInterimTranscript;

                                    // Send as final transcript to client
                                    ws.send(JSON.stringify({
                                        type: 'transcript',
                                        transcript: deepgramWs.lastInterimTranscript,
                                        is_final: true,
                                        session_id: sessionId
                                    }));

                                    try {
                                        // Process as final
                                        await processFullConversation(ws, deepgramWs.lastInterimTranscript, sessionId);
                                    } finally {
                                        // Always reset processing flag
                                        deepgramWs.processingInProgress = false;
                                        console.log(`✅ Timeout processing completed for: "${deepgramWs.lastInterimTranscript}" (requestId: ${timeoutRequestId})`);
                                    }

                                    deepgramWs.finalTranscriptTimeout = null;
                                }, 2000); // 2 second timeout - faster response
                                } else {
                                    console.log(`🚫 Ignoring interim transcript after final: "${transcript}"`);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error parsing Deepgram response:', error);
                }
        });

            deepgramWs.on('error', (error) => {
                console.error(`❌ Deepgram error for session ${sessionId}:`, error);
                console.error(`❌ Error details:`, error.message, error.code);

                // Clean up connection on error
                deepgramConnections.delete(sessionId);

                ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Deepgram connection error - will retry on next audio'
                }));
            });

            deepgramWs.on('close', (code, reason) => {
                console.log(`🔌 Deepgram persistent connection closed for session ${sessionId} (code: ${code}, reason: ${reason})`);

                // Clean up keep-alive interval
                if (deepgramWs.keepAliveInterval) {
                    clearInterval(deepgramWs.keepAliveInterval);
                    console.log(`🧹 Keep-alive interval cleared for session ${sessionId}`);
                }

                // Clean up final transcript timeout
                if (deepgramWs && deepgramWs.finalTranscriptTimeout) {
                    clearTimeout(deepgramWs.finalTranscriptTimeout);
                    deepgramWs.finalTranscriptTimeout = null;
                    console.log(`🧹 Final transcript timeout cleared for session ${sessionId}`);
                }

                // Clean up connection
                deepgramConnections.delete(sessionId);

                // Log specific close codes for debugging
                if (code === 1011) {
                    console.log(`⚠️ Deepgram timeout (1011) - will recreate connection on next audio`);
                } else if (code === 1000) {
                    console.log(`✅ Deepgram normal close (1000)`);
                } else {
                    console.log(`⚠️ Deepgram unexpected close code: ${code} - will recreate connection`);
                }
            });

            // Store the new connection
            deepgramConnections.set(sessionId, deepgramWs);
        } else {
            // Reuse existing persistent connection
            console.log(`♻️ Reusing persistent Deepgram connection for session ${sessionId}`);
            console.log(`📡 Sending audio to persistent stream (${audioData.length} bytes)`);

            try {
                deepgramWs.send(audioData);
                console.log(`📡 Audio sent to persistent stream`);
                // Set current request ID for this audio stream
                deepgramWs.currentRequestId = requestId;
                console.log(`🆔 Updated currentRequestId: ${requestId}`);
            } catch (error) {
                console.error(`❌ Error sending audio to persistent stream:`, error);
                // Clean up and mark for reconnection
                if (deepgramWs.keepAliveInterval) {
                    clearInterval(deepgramWs.keepAliveInterval);
                }
                deepgramConnections.delete(sessionId);
            }
        }

        // Basic audio validation
        if (!audioData || audioData.length === 0) {
            console.error('❌ Invalid audio data - empty or null');
            return;
        }

        console.log(`📊 Audio processing complete for session ${sessionId}`);

    } catch (error) {
        console.error(`❌ Error in handleAudioStream for session ${sessionId}:`, error);

        // Clean up connection on error
        deepgramConnections.delete(sessionId);

        ws.send(JSON.stringify({
            type: 'error',
            message: 'Audio processing error'
        }));
    }
}

// Process full conversation: transcript -> OpenAI -> TTS -> audio stream
async function processFullConversation(ws, transcript, sessionId) {
    try {
        console.log(`🤖 Starting full conversation processing for: "${transcript}"`);

        // Send status update
        ws.send(JSON.stringify({
            type: 'status',
            message: 'Spracovávam vašu otázku...',
            session_id: sessionId
        }));

        // Call OpenAI
        const aiResponse = await openai.chatCompletion(transcript, []);

        if (aiResponse) {
            console.log(`🤖 OpenAI response: "${aiResponse}"`);

            // Send AI response to client
            ws.send(JSON.stringify({
                type: 'ai_response',
                response: aiResponse,
                session_id: sessionId
            }));

            // Send status update
            ws.send(JSON.stringify({
                type: 'status',
                message: 'Generujem audio odpoveď...',
                session_id: sessionId
            }));

            // Generate TTS audio
            const audioFilePath = await tts.synthesize(aiResponse, 'sk_SK-lili-medium');

            if (audioFilePath) {
                console.log(`🔊 TTS audio file generated: ${audioFilePath}`);

                // Read audio file as buffer
                const fs = require('fs').promises;
                const audioBuffer = await fs.readFile(audioFilePath);

                console.log(`📁 Audio file read: ${audioBuffer.length} bytes`);

                // Send audio data as binary through WebSocket
                ws.send(JSON.stringify({
                    type: 'audio_ready',
                    audio_size: audioBuffer.length,
                    session_id: sessionId
                }));

                // Send binary audio data
                ws.send(audioBuffer);

                console.log(`📡 Audio streamed to client (${audioBuffer.length} bytes)`);
            } else {
                console.log('❌ TTS synthesis failed');
                ws.send(JSON.stringify({
                    type: 'error',
                    message: 'TTS synthesis failed',
                    session_id: sessionId
                }));
            }

        } else {
            console.log('❌ OpenAI chat failed');
            ws.send(JSON.stringify({
                type: 'error',
                message: 'OpenAI chat failed',
                session_id: sessionId
            }));
        }

    } catch (error) {
        console.error('Error in processFullConversation:', error);
        ws.send(JSON.stringify({
            type: 'error',
            message: error.message,
            session_id: sessionId
        }));
    }
}

// Duplicitná funkcia odstránená - používame len prvú definíciu

async function handleWebSocketMessage(ws, message, sessionId) {
    switch (message.type) {
        case 'ping':
            ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
            break;
        
        case 'audio_chunk':
            // Handle audio chunk
            break;
            
        default:
            console.log('Unknown WebSocket message type:', message.type);
    }
}

// Debug endpoint for Deepgram testing
app.post('/api/debug-deepgram', upload.single('audio'), async (req, res) => {
    try {
        console.log('🔍 Debug Deepgram request received');

        if (!req.file) {
            return res.status(400).json({ error: 'No audio file provided' });
        }

        const audioBuffer = req.file.buffer;
        console.log(`🔍 Audio buffer size: ${audioBuffer.length} bytes`);
        console.log(`🔍 Audio mimetype: ${req.file.mimetype}`);

        // Test basic Deepgram connection
        const testResult = await deepgram.transcribe(audioBuffer);
        console.log(`🔍 Deepgram result: "${testResult}"`);

        res.json({
            success: true,
            transcript: testResult,
            audioSize: audioBuffer.length,
            mimetype: req.file.mimetype
        });

    } catch (error) {
        console.error('🔍 Debug Deepgram error:', error.message);
        res.status(500).json({
            error: error.message,
            details: error.stack
        });
    }
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Shutting down gracefully...');
    server.close(() => {
        process.exit(0);
    });
});

// Periodic cleanup of expired sessions
setInterval(async () => {
    try {
        await sessionManager.cleanupExpiredSessions();
    } catch (error) {
        console.error('❌ Session cleanup error:', error);
    }
}, 5 * 60 * 1000); // Every 5 minutes

// Start HTTP server
server.listen(config.port, '0.0.0.0', () => {
    console.log(`🚀 Fast Voice Server (Worker ${process.pid}) HTTP listening on port ${config.port}`);
    console.log(`📊 Metrics available at http://*************:${config.port}/api/metrics`);
    console.log(`🎤 Voice chat at http://*************:${config.port}/api/voice-chat`);
    console.log(`🔌 WebSocket at ws://*************:${config.wsPort || config.port}`);
    console.log(`🌐 Cloudflare frontend ready for connection`);
    console.log(`👥 Session management: Enhanced with parallel support`);
    console.log(`🔄 Auto-cleanup: Every 5 minutes`);
});

module.exports = app;
