module.exports = {
  apps: [{
    name: 'fast-voice-server',
    script: 'server.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 80,
      WS_PORT: 5001,
      ENABLE_CLUSTERING: 'false',
      DEEPGRAM_API_KEY: '****************************************',
      OPENAI_API_KEY: '***************************************************'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 80,
      WS_PORT: 5001
    },
    max_memory_restart: '1G',
    error_file: '/var/log/fast-voice-server/error.log',
    out_file: '/var/log/fast-voice-server/out.log',
    log_file: '/var/log/fast-voice-server/combined.log',
    time: true,
    autorestart: true,
    watch: false,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
