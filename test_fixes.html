<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Voice Chat Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        audio {
            width: 100%;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Test Voice Chat Fixes</h1>
        <p>Testovanie opráv pre Piper TTS a Deepgram STT</p>

        <!-- Test 1: Piper TTS -->
        <div class="test-section">
            <h3>🔊 Test 1: Piper TTS (166B problém)</h3>
            <p>Testuje či Piper generuje správne WAV súbory s dostatočnou veľkosťou</p>
            <input type="text" id="ttsText" placeholder="Zadajte text pre TTS..." value="Ahoj! Áno, počujem ťa." style="width: 300px; padding: 5px;">
            <button onclick="testTTS()">Test TTS</button>
            <div id="ttsLog" class="log"></div>
            <audio id="ttsAudio" controls style="display: none;"></audio>
        </div>

        <!-- Test 2: Deepgram STT -->
        <div class="test-section">
            <h3>🎤 Test 2: Deepgram STT (500 Error problém)</h3>
            <p>Testuje či Deepgram správne spracováva WebM audio z MediaRecorder</p>
            <button id="recordBtn" onclick="startRecording()">🎤 Začať nahrávanie</button>
            <button id="stopBtn" onclick="stopRecording()" disabled>⏹️ Zastaviť nahrávanie</button>
            <button id="transcribeBtn" onclick="testTranscription()" disabled>📝 Transkribovať</button>
            <div id="sttLog" class="log"></div>
            <audio id="recordedAudio" controls style="display: none;"></audio>
        </div>

        <!-- Test 3: Kompletný workflow -->
        <div class="test-section">
            <h3>🔄 Test 3: Kompletný Voice Chat Workflow</h3>
            <p>Testuje celý workflow: MediaRecorder → Deepgram → OpenAI → Piper → Audio playback</p>
            <button id="voiceChatBtn" onclick="startVoiceChat()">🎤 Spustiť Voice Chat</button>
            <div id="workflowLog" class="log"></div>
            <audio id="responseAudio" controls style="display: none;"></audio>
        </div>
    </div>

    <script>
        const API_BASE = 'https://eb55c2a1.chatko-voice.pages.dev';
        let mediaRecorder;
        let audioChunks = [];

        function log(message, type = 'info', logElement = 'ttsLog') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById(logElement);
            const className = type;
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Test 1: Piper TTS
        async function testTTS() {
            const text = document.getElementById('ttsText').value.trim();
            if (!text) {
                log('❌ Zadajte text pre TTS', 'error');
                return;
            }

            log(`🔊 Testujem TTS pre text: "${text}"`, 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/speak`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: text })
                });

                log(`📡 TTS Response: ${response.status} ${response.statusText}`, 'info');

                if (response.ok) {
                    const audioBlob = await response.blob();
                    log(`✅ TTS úspešný: ${audioBlob.size} bajtov (${audioBlob.type})`, 'success');
                    
                    if (audioBlob.size < 1044) {
                        log(`⚠️ PROBLÉM: Audio príliš malé (${audioBlob.size}B), očakávané >1044B`, 'warning');
                    } else {
                        log(`✅ Audio veľkosť OK: ${audioBlob.size} bajtov`, 'success');
                    }

                    // Prehrať audio
                    const audioUrl = URL.createObjectURL(audioBlob);
                    const audioElement = document.getElementById('ttsAudio');
                    audioElement.src = audioUrl;
                    audioElement.style.display = 'block';
                    audioElement.play();
                } else {
                    const errorData = await response.json();
                    log(`❌ TTS chyba: ${JSON.stringify(errorData)}`, 'error');
                }
            } catch (error) {
                log(`❌ TTS network chyba: ${error.message}`, 'error');
            }
        }

        // Test 2: Deepgram STT
        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                // Skúsime rôzne formáty pre lepšiu kompatibilitu
                let mimeType = 'audio/webm;codecs=opus';
                if (MediaRecorder.isTypeSupported('audio/wav')) {
                    mimeType = 'audio/wav';
                    log('🎤 Používam WAV formát', 'info', 'sttLog');
                } else if (MediaRecorder.isTypeSupported('audio/webm;codecs=pcm')) {
                    mimeType = 'audio/webm;codecs=pcm';
                    log('🎤 Používam WebM PCM formát', 'info', 'sttLog');
                } else {
                    log('🎤 Používam WebM Opus formát (fallback)', 'warning', 'sttLog');
                }

                mediaRecorder = new MediaRecorder(stream, { mimeType: mimeType });
                audioChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    log(`🎵 Nahrávka dokončená (${audioChunks.length} chunks)`, 'info', 'sttLog');
                    document.getElementById('transcribeBtn').disabled = false;
                };

                mediaRecorder.start(100); // 100ms chunks
                log('✅ Nahrávanie spustené', 'success', 'sttLog');
                
                document.getElementById('recordBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
            } catch (error) {
                log(`❌ Chyba pri spustení nahrávania: ${error.message}`, 'error', 'sttLog');
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                
                document.getElementById('recordBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                
                log('⏹️ Nahrávanie zastavené', 'info', 'sttLog');
            }
        }

        async function testTranscription() {
            if (audioChunks.length === 0) {
                log('❌ Žiadne audio chunks na transkripciu', 'error', 'sttLog');
                return;
            }

            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            log(`🎵 Audio blob: ${audioBlob.size} bajtov, typ: ${audioBlob.type}`, 'info', 'sttLog');

            // Zobraziť nahraté audio
            const audioUrl = URL.createObjectURL(audioBlob);
            const audioElement = document.getElementById('recordedAudio');
            audioElement.src = audioUrl;
            audioElement.style.display = 'block';

            // Odoslať na transkripciu
            const formData = new FormData();
            formData.append('audio', audioBlob, 'recording.webm');

            try {
                log('📡 Posielam audio na Deepgram...', 'info', 'sttLog');
                const response = await fetch(`${API_BASE}/api/transcribe`, {
                    method: 'POST',
                    body: formData
                });

                log(`📡 Deepgram Response: ${response.status} ${response.statusText}`, 'info', 'sttLog');

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Transkripcia úspešná: "${data.transcript}"`, 'success', 'sttLog');
                    log(`📊 Audio info: ${data.audio_size}B, typ: ${data.audio_type}`, 'info', 'sttLog');
                } else {
                    const errorData = await response.json();
                    log(`❌ Deepgram chyba: ${JSON.stringify(errorData)}`, 'error', 'sttLog');
                }
            } catch (error) {
                log(`❌ Network chyba: ${error.message}`, 'error', 'sttLog');
            }
        }

        // Test 3: Kompletný workflow
        async function startVoiceChat() {
            log('🎤 Spúšťam kompletný voice chat workflow...', 'info', 'workflowLog');
            
            // Simulujeme nahrávanie (použijeme existujúce audio chunks ak sú dostupné)
            if (audioChunks.length === 0) {
                log('❌ Najprv nahrajte audio v Test 2', 'error', 'workflowLog');
                return;
            }

            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            const formData = new FormData();
            formData.append('audio', audioBlob, 'voice-chat.webm');

            try {
                log('📡 Posielam na voice-chat endpoint...', 'info', 'workflowLog');
                const response = await fetch(`${API_BASE}/api/voice-chat`, {
                    method: 'POST',
                    body: formData
                });

                log(`📡 Voice Chat Response: ${response.status} ${response.statusText}`, 'info', 'workflowLog');

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Transkripcia: "${data.transcript}"`, 'success', 'workflowLog');
                    log(`🤖 AI odpoveď: "${data.response}"`, 'success', 'workflowLog');
                    
                    if (data.audio) {
                        log(`🔊 TTS audio získané (base64)`, 'success', 'workflowLog');
                        
                        // Dekódovať base64 audio a prehrať
                        const audioData = atob(data.audio);
                        const audioArray = new Uint8Array(audioData.length);
                        for (let i = 0; i < audioData.length; i++) {
                            audioArray[i] = audioData.charCodeAt(i);
                        }
                        const audioBlob = new Blob([audioArray], { type: 'audio/wav' });
                        
                        const audioUrl = URL.createObjectURL(audioBlob);
                        const audioElement = document.getElementById('responseAudio');
                        audioElement.src = audioUrl;
                        audioElement.style.display = 'block';
                        audioElement.play();
                        
                        log(`✅ Kompletný workflow úspešný!`, 'success', 'workflowLog');
                    } else {
                        log(`⚠️ TTS nedostupné: ${data.warning}`, 'warning', 'workflowLog');
                    }
                } else {
                    const errorData = await response.json();
                    log(`❌ Voice Chat chyba: ${JSON.stringify(errorData)}`, 'error', 'workflowLog');
                }
            } catch (error) {
                log(`❌ Network chyba: ${error.message}`, 'error', 'workflowLog');
            }
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Test stránka načítaná', 'success');
            log(`📡 API Base: ${API_BASE}`, 'info');
        });
    </script>
</body>
</html>
