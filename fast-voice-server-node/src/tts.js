/**
 * Optimized TTS Engine
 * High-performance text-to-speech with <PERSON> and caching
 */

const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

class TTSEngine {
    constructor(piperPath, modelPath) {
        // Try local Piper first, then Oracle server paths
        this.piperPath = piperPath || this.findPiperPath();
        this.modelPath = modelPath || this.findModelPath();
        this.tempDir = '/tmp/tts_cache';
        this.cache = new Map(); // In-memory cache for file paths
        this.maxCacheSize = 1000;

        this.initializeAsync();
    }

    findPiperPath() {
        // Check for local Piper installation first
        const localPaths = [
            'piper', // System PATH
            '/Users/<USER>/.pyenv/versions/3.10.11/bin/piper', // Direct path to installed Piper
            '/usr/local/bin/piper',
            '/opt/homebrew/bin/piper'
        ];

        // Oracle server path (updated to correct location)
        const oraclePath = '/usr/local/bin/piper';

        // Try local paths first
        for (const piperPath of localPaths) {
            try {
                if (piperPath === 'piper') {
                    // Test system PATH piper
                    require('child_process').execSync('which piper', { stdio: 'ignore' });
                    console.log(`🎵 Found local Piper: ${piperPath}`);
                    return piperPath;
                } else {
                    // Test specific path
                    const fs = require('fs');
                    if (fs.existsSync(piperPath)) {
                        console.log(`🎵 Found local Piper: ${piperPath}`);
                        return piperPath;
                    }
                }
            } catch (e) {
                // Continue to next path
            }
        }

        // Fallback to Oracle server path
        console.log(`🎵 Using Oracle server Piper path: ${oraclePath}`);
        return oraclePath;
    }

    findModelPath() {
        const fs = require('fs');
        const path = require('path');

        // Local model paths (absolute paths)
        const localPaths = [
            path.join(process.cwd(), 'models/sk_SK-lili-medium.onnx'),
            path.join(process.cwd(), 'piper_local/models/sk_SK-lili-medium.onnx'),
            path.join(process.cwd(), '../models/sk_SK-lili-medium.onnx'),
            '/Users/<USER>/Documents/augment-projects/chatko/piper_local/models/sk_SK-lili-medium.onnx'
        ];

        // Oracle server path (updated to correct location)
        const oraclePath = '/home/<USER>/piper-voices/sk_SK-lili-medium.onnx';

        // Try local paths first
        for (const modelPath of localPaths) {
            if (fs.existsSync(modelPath)) {
                console.log(`🎵 Found local model: ${modelPath}`);
                return modelPath;
            }
        }

        // Fallback to Oracle server path
        console.log(`🎵 Using Oracle server model path: ${oraclePath}`);
        return oraclePath;
    }
    
    async initializeAsync() {
        try {
            // Create temp directory
            await fs.mkdir(this.tempDir, { recursive: true });

            // Check if Piper is available (local or Oracle server)
            let piperAvailable = false;
            let modelAvailable = false;

            // Test Piper executable
            try {
                if (this.piperPath === 'piper') {
                    // Test system PATH piper
                    require('child_process').execSync('piper --help', { stdio: 'ignore' });
                    piperAvailable = true;
                } else {
                    // Test specific path
                    await fs.access(this.piperPath);
                    piperAvailable = true;
                }
            } catch (e) {
                console.log(`⚠️ Piper not found at: ${this.piperPath}`);
            }

            // Test model file
            try {
                await fs.access(this.modelPath);
                modelAvailable = true;
            } catch (e) {
                console.log(`⚠️ Model not found at: ${this.modelPath}`);
            }

            if (!piperAvailable || !modelAvailable) {
                console.log('🔊 TTS Engine initialized in DEVELOPMENT mode (Piper/Model not available)');
                console.log('🎵 TTS will return mock audio URLs');
                console.log(`🎵 Piper available: ${piperAvailable}, Model available: ${modelAvailable}`);
                this.developmentMode = true;
                return;
            }

            console.log('🔊 TTS Engine initialized in PRODUCTION mode');
            console.log(`🎵 Using Piper: ${this.piperPath}`);
            console.log(`🎵 Using model: ${this.modelPath}`);

            this.developmentMode = false;

            // Skip preloading to save disk space
            console.log('🔇 Skipping TTS preloading to save disk space');

        } catch (error) {
            console.error('TTS initialization error:', error.message);
            throw new Error(`TTS Engine initialization failed: ${error.message}`);
        }
    }
    
    async synthesize(text) {
        const startTime = Date.now();

        try {
            // Development mode - return mock audio URL
            if (this.developmentMode) {
                const duration = Date.now() - startTime;
                console.log(`🔊 TTS synthesis (MOCK): ${duration}ms - "${text.substring(0, 50)}..."`);
                return `/mock-audio/${this.generateCacheKey(text)}.wav`;
            }

            // Generate cache key
            const cacheKey = this.generateCacheKey(text);

            // Check cache first
            if (this.cache.has(cacheKey)) {
                const cachedPath = this.cache.get(cacheKey);

                try {
                    await fs.access(cachedPath);
                    console.log(`🎵 TTS cache hit: ${Date.now() - startTime}ms`);
                    return cachedPath;
                } catch {
                    // File doesn't exist, remove from cache
                    this.cache.delete(cacheKey);
                }
            }

            // Generate new audio
            const audioPath = await this.generateAudio(text);

            // Cache the result
            this.cache.set(cacheKey, audioPath);

            // Limit cache size
            if (this.cache.size > this.maxCacheSize) {
                const oldestKey = this.cache.keys().next().value;
                const oldPath = this.cache.get(oldestKey);
                this.cache.delete(oldestKey);

                // Clean up old file
                try {
                    await fs.unlink(oldPath);
                } catch (error) {
                    console.warn('Failed to clean up old TTS file:', error.message);
                }
            }

            const duration = Date.now() - startTime;
            console.log(`🔊 TTS synthesis: ${duration}ms - "${text.substring(0, 50)}..."`);

            return audioPath;

        } catch (error) {
            console.error('TTS synthesis error:', error.message);
            throw new Error(`TTS synthesis failed: ${error.message}`);
        }
    }
    
    async generateAudio(text) {
        return new Promise((resolve, reject) => {
            const audioId = uuidv4();
            const outputPath = path.join(this.tempDir, `${audioId}.wav`);
            
            // Spawn Piper process with optimized settings
            const piper = spawn(this.piperPath, [
                '--model', this.modelPath,
                '--output_file', outputPath,
                '--length_scale', '1.0',    // Normal speed
                '--noise_scale', '0.667',   // Slight variation
                '--noise_w', '0.8'          // Phoneme duration variation
            ], {
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            let stdout = '';
            let stderr = '';
            
            piper.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            
            piper.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            
            piper.on('close', async (code) => {
                if (code === 0) {
                    try {
                        // Verify output file exists and has content
                        const stats = await fs.stat(outputPath);
                        if (stats.size > 0) {
                            resolve(outputPath);
                        } else {
                            reject(new Error('TTS output file is empty'));
                        }
                    } catch (error) {
                        reject(new Error('TTS output file not created'));
                    }
                } else {
                    reject(new Error(`Piper TTS failed with code ${code}: ${stderr}`));
                }
            });
            
            piper.on('error', (error) => {
                reject(new Error(`Piper process error: ${error.message}`));
            });
            
            // Write text to stdin and close
            piper.stdin.write(text);
            piper.stdin.end();
        });
    }
    
    generateCacheKey(text) {
        return crypto.createHash('md5').update(text).digest('hex');
    }
    
    async preloadCommonPhrases() {
        if (this.developmentMode) {
            console.log('🔄 Skipping TTS phrase preloading in development mode');
            return;
        }

        const commonPhrases = [
            'Ahoj, ako sa máš?',
            'Ďakujem za otázku.',
            'Prepáčte, nerozumiem.',
            'Môžete to zopakovať?',
            'To je zaujímavé.',
            'Súhlasím s vami.',
            'Nie som si istý.',
            'Skúsim vám pomôcť.',
            'Máte ešte nejakú otázku?',
            'Rád som vám pomohol.'
        ];

        console.log(`🔄 Preloading ${commonPhrases.length} common TTS phrases...`);

        const preloadPromises = commonPhrases.map(async (phrase) => {
            try {
                await this.synthesize(phrase);
            } catch (error) {
                console.warn(`Failed to preload phrase "${phrase}":`, error.message);
            }
        });

        await Promise.all(preloadPromises);
        console.log('✅ TTS phrase preloading completed');
    }
    
    async cleanupOldFiles() {
        try {
            const files = await fs.readdir(this.tempDir);
            const now = Date.now();
            let cleaned = 0;
            
            for (const file of files) {
                const filePath = path.join(this.tempDir, file);
                try {
                    const stats = await fs.stat(filePath);
                    const age = now - stats.mtime.getTime();
                    
                    // Remove files older than 1 hour
                    if (age > 3600000) {
                        await fs.unlink(filePath);
                        cleaned++;
                    }
                } catch (error) {
                    console.warn(`Failed to process file ${file}:`, error.message);
                }
            }
            
            if (cleaned > 0) {
                console.log(`🧹 Cleaned up ${cleaned} old TTS files`);
            }
            
        } catch (error) {
            console.warn('TTS cleanup error:', error.message);
        }
    }
    
    // Health check
    async healthCheck() {
        try {
            const testPath = await this.synthesize('Test');
            await fs.unlink(testPath); // Clean up test file
            return true;
        } catch (error) {
            console.error('TTS health check failed:', error.message);
            return false;
        }
    }
    
    // Get engine statistics
    getStats() {
        return {
            piperPath: this.piperPath,
            modelPath: this.modelPath,
            tempDir: this.tempDir,
            cacheSize: this.cache.size,
            maxCacheSize: this.maxCacheSize
        };
    }
    
    // Start periodic cleanup
    startCleanupTimer() {
        setInterval(() => {
            this.cleanupOldFiles();
        }, 3600000); // Every hour
        
        console.log('🧹 TTS cleanup timer started (hourly)');
    }
}

module.exports = TTSEngine;
