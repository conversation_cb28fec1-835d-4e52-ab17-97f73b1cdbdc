#!/usr/bin/env node

/**
 * Enhanced WebSocket Handler for Fast Voice Server
 * Manages multiple concurrent WebSocket connections with session isolation
 */

const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

class WebSocketHandler {
    constructor(sessionManager, deepgramClient, openaiClient, ttsEngine) {
        this.sessionManager = sessionManager;
        this.deepgramClient = deepgramClient;
        this.openaiClient = openaiClient;
        this.ttsEngine = ttsEngine;
        
        // Track WebSocket connections per session
        this.connections = new Map();
        
        // Track Deepgram connections per session
        this.deepgramConnections = new Map();
        
        // Connection metrics
        this.connectionMetrics = {
            totalConnections: 0,
            activeConnections: 0,
            totalMessages: 0,
            errors: 0
        };
        
        console.log('🔌 WebSocket Handler initialized');
    }
    
    /**
     * Handle new WebSocket connection
     */
    async handleConnection(ws, req) {
        const sessionId = await this.sessionManager.createSession({
            userAgent: req.headers['user-agent'],
            ip: req.connection.remoteAddress,
            timestamp: Date.now()
        });
        
        // Store connection
        this.connections.set(sessionId, {
            ws,
            sessionId,
            connected: Date.now(),
            lastActivity: Date.now(),
            messageCount: 0
        });
        
        // Update metrics
        this.connectionMetrics.totalConnections++;
        this.connectionMetrics.activeConnections++;
        
        console.log(`🔌 WebSocket connected: ${sessionId} (Active: ${this.connectionMetrics.activeConnections})`);
        
        // Set up event handlers
        this.setupConnectionHandlers(ws, sessionId);
        
        // Send welcome message
        this.sendMessage(sessionId, {
            type: 'connected',
            session_id: sessionId,
            message: 'Connected to Fast Voice Server',
            timestamp: Date.now()
        });
        
        return sessionId;
    }
    
    /**
     * Set up WebSocket event handlers for a connection
     */
    setupConnectionHandlers(ws, sessionId) {
        // Handle incoming messages
        ws.on('message', async (data) => {
            try {
                await this.handleMessage(sessionId, data);
            } catch (error) {
                console.error(`❌ Message handling error for session ${sessionId}:`, error);
                this.sendError(sessionId, 'Message processing failed', error.message);
                this.connectionMetrics.errors++;
            }
        });
        
        // Handle connection close
        ws.on('close', async (code, reason) => {
            await this.handleDisconnection(sessionId, code, reason);
        });
        
        // Handle connection errors
        ws.on('error', (error) => {
            console.error(`❌ WebSocket error for session ${sessionId}:`, error);
            this.connectionMetrics.errors++;
        });
        
        // Set up ping/pong for connection health
        const pingInterval = setInterval(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.ping();
            } else {
                clearInterval(pingInterval);
            }
        }, 30000); // Ping every 30 seconds
        
        ws.on('pong', () => {
            this.updateConnectionActivity(sessionId);
        });
    }
    
    /**
     * Handle incoming WebSocket message
     */
    async handleMessage(sessionId, data) {
        const connection = this.connections.get(sessionId);
        if (!connection) {
            console.warn(`⚠️ Message received for unknown session: ${sessionId}`);
            return;
        }
        
        // Update activity
        this.updateConnectionActivity(sessionId);
        connection.messageCount++;
        this.connectionMetrics.totalMessages++;
        
        try {
            // Handle binary audio data
            if (Buffer.isBuffer(data)) {
                await this.handleAudioStream(sessionId, data);
                return;
            }
            
            // Handle JSON messages
            const message = JSON.parse(data.toString());
            await this.handleJsonMessage(sessionId, message);
            
        } catch (error) {
            console.error(`❌ Error processing message for session ${sessionId}:`, error);
            this.sendError(sessionId, 'Invalid message format');
        }
    }
    
    /**
     * Handle JSON messages
     */
    async handleJsonMessage(sessionId, message) {
        switch (message.type) {
            case 'ping':
                this.sendMessage(sessionId, {
                    type: 'pong',
                    timestamp: Date.now()
                });
                break;

            case 'audio_stream':
                // Audio metadata received, binary data will follow
                console.log(`📡 Audio stream metadata for session ${sessionId}: ${message.audio_size} bytes, format: ${message.audio_format}`);
                // Store metadata for next binary message
                const connection = this.connections.get(sessionId);
                if (connection) {
                    connection.pendingAudioSize = message.audio_size;
                    connection.pendingAudioFormat = message.audio_format;
                }
                break;

            case 'text_message':
                await this.handleTextMessage(sessionId, message.text);
                break;

            case 'get_history':
                await this.handleGetHistory(sessionId, message.limit);
                break;

            case 'clear_history':
                await this.handleClearHistory(sessionId);
                break;

            default:
                console.log(`❓ Unknown message type for session ${sessionId}:`, message.type);
                this.sendError(sessionId, 'Unknown message type', message.type);
        }
    }
    
    /**
     * Handle audio stream - use same logic as server.js
     */
    async handleAudioStream(sessionId, audioData) {
        console.log(`🎤 Audio stream received for session ${sessionId}: ${audioData.length} bytes`);

        try {
            // Use the same handleAudioStream function from server.js
            const connection = this.connections.get(sessionId);
            if (!connection) {
                throw new Error('Connection not found');
            }

            // Call the main server's handleAudioStream function
            // This will handle Deepgram WebSocket connection and full pipeline
            await global.handleAudioStream(connection.ws, audioData, sessionId);

        } catch (error) {
            console.error(`❌ Audio processing error for session ${sessionId}:`, error);
            this.sendError(sessionId, 'Audio processing failed', error.message);
        }
    }
    
    /**
     * Process voice pipeline for a session
     */
    async processVoicePipeline(sessionId, audioData) {
        const startTime = Date.now();
        
        // Get conversation history for context
        const history = await this.sessionManager.getConversationHistory(sessionId);
        
        // Step 1: Speech-to-text
        const sttStart = Date.now();
        const transcript = await this.deepgramClient.transcribe(audioData);
        const sttLatency = Date.now() - sttStart;
        
        if (!transcript || transcript.trim().length === 0) {
            throw new Error('No speech detected');
        }
        
        // Step 2: Generate AI response
        const llmStart = Date.now();
        const aiResponse = await this.openaiClient.chat(transcript, history);
        const llmLatency = Date.now() - llmStart;
        
        // Step 3: Text-to-speech
        const ttsStart = Date.now();
        const audioPath = await this.ttsEngine.synthesize(aiResponse);
        const ttsLatency = Date.now() - ttsStart;
        
        // Update conversation history
        await this.sessionManager.addMessage(sessionId, 'user', transcript);
        await this.sessionManager.addMessage(sessionId, 'assistant', aiResponse);
        
        // Record metrics
        const totalLatency = Date.now() - startTime;
        await this.sessionManager.recordLatency(sessionId, {
            stt: sttLatency,
            llm: llmLatency,
            tts: ttsLatency,
            total: totalLatency
        });
        
        return {
            transcript,
            response: aiResponse,
            audio_path: audioPath,
            latency: {
                stt_ms: sttLatency,
                llm_ms: llmLatency,
                tts_ms: ttsLatency,
                total_ms: totalLatency
            }
        };
    }
    
    /**
     * Handle text message
     */
    async handleTextMessage(sessionId, text) {
        try {
            const history = await this.sessionManager.getConversationHistory(sessionId);
            const response = await this.openaiClient.chat(text, history);
            
            await this.sessionManager.addMessage(sessionId, 'user', text);
            await this.sessionManager.addMessage(sessionId, 'assistant', response);
            
            this.sendMessage(sessionId, {
                type: 'text_response',
                user_message: text,
                ai_response: response,
                timestamp: Date.now()
            });
            
        } catch (error) {
            this.sendError(sessionId, 'Text processing failed', error.message);
        }
    }
    
    /**
     * Handle get conversation history
     */
    async handleGetHistory(sessionId, limit = 10) {
        try {
            const history = await this.sessionManager.getConversationHistory(sessionId, limit);
            
            this.sendMessage(sessionId, {
                type: 'conversation_history',
                history,
                count: history.length,
                timestamp: Date.now()
            });
            
        } catch (error) {
            this.sendError(sessionId, 'Failed to get history', error.message);
        }
    }
    
    /**
     * Handle clear conversation history
     */
    async handleClearHistory(sessionId) {
        try {
            const sessionData = await this.sessionManager.getSessionData(sessionId);
            if (sessionData) {
                sessionData.conversationHistory = [];
                await this.sessionManager.setSessionData(sessionId, sessionData);
            }
            
            this.sendMessage(sessionId, {
                type: 'history_cleared',
                message: 'Conversation history cleared',
                timestamp: Date.now()
            });
            
        } catch (error) {
            this.sendError(sessionId, 'Failed to clear history', error.message);
        }
    }
    
    /**
     * Send message to a specific session
     */
    sendMessage(sessionId, message) {
        const connection = this.connections.get(sessionId);
        if (connection && connection.ws.readyState === WebSocket.OPEN) {
            connection.ws.send(JSON.stringify(message));
            return true;
        }
        return false;
    }
    
    /**
     * Send error message to a specific session
     */
    sendError(sessionId, error, details = null) {
        this.sendMessage(sessionId, {
            type: 'error',
            error,
            details,
            timestamp: Date.now()
        });
    }
    
    /**
     * Update connection activity timestamp
     */
    updateConnectionActivity(sessionId) {
        const connection = this.connections.get(sessionId);
        if (connection) {
            connection.lastActivity = Date.now();
        }
        this.sessionManager.updateActivity(sessionId);
    }
    
    /**
     * Handle connection disconnection
     */
    async handleDisconnection(sessionId, code, reason) {
        console.log(`🔌 WebSocket disconnected: ${sessionId} (Code: ${code})`);
        
        // Clean up Deepgram connection
        const deepgramWs = this.deepgramConnections.get(sessionId);
        if (deepgramWs && deepgramWs.readyState === WebSocket.OPEN) {
            deepgramWs.close();
            this.deepgramConnections.delete(sessionId);
        }
        
        // Remove connection
        this.connections.delete(sessionId);
        
        // Close session
        await this.sessionManager.closeSession(sessionId);
        
        // Update metrics
        this.connectionMetrics.activeConnections--;
        
        console.log(`🔒 Session ${sessionId} cleaned up (Active: ${this.connectionMetrics.activeConnections})`);
    }
    
    /**
     * Get connection statistics
     */
    getConnectionStats() {
        return {
            ...this.connectionMetrics,
            connections: Array.from(this.connections.values()).map(conn => ({
                sessionId: conn.sessionId,
                connected: conn.connected,
                lastActivity: conn.lastActivity,
                messageCount: conn.messageCount
            }))
        };
    }
    
    /**
     * Broadcast message to all active connections
     */
    broadcast(message) {
        let sentCount = 0;
        for (const [sessionId, connection] of this.connections.entries()) {
            if (this.sendMessage(sessionId, message)) {
                sentCount++;
            }
        }
        return sentCount;
    }
}

module.exports = WebSocketHandler;
