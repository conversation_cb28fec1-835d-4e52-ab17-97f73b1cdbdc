/**
 * Cloudflare Pages Function - Oracle Backend TTS Proxy
 */

const ORACLE_BACKEND = '129.159.9.170';  // Production Oracle server

export async function onRequestPost(context) {
    try {
        console.log('🔧 Proxying TTS request to Oracle backend...');
        
        const requestBody = await context.request.text();
        console.log(`📡 TTS request: ${requestBody}`);
        
        // Forward request to Oracle backend
        const oracleResponse = await fetch(`https://${ORACLE_BACKEND}/api/speak`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'audio/wav',
                'User-Agent': 'Cloudflare-Pages-Function/1.0'
            },
            body: requestBody
        });
        
        console.log(`📡 Oracle TTS response: ${oracleResponse.status}`);
        
        if (oracleResponse.ok) {
            const contentType = oracleResponse.headers.get('content-type') || 'audio/wav';
            const audioBuffer = await oracleResponse.arrayBuffer();
            console.log(`✅ TTS successful: ${audioBuffer.byteLength} bytes`);

            // Kontrola minimálnej veľkosti audio súboru
            if (audioBuffer.byteLength < 1044) {  // 44 byte WAV header + min 1000 bytes audio
                console.error(`❌ TTS audio too small: ${audioBuffer.byteLength} bytes`);
                return new Response(JSON.stringify({
                    error: 'TTS audio too small',
                    details: `Generated audio is only ${audioBuffer.byteLength} bytes, minimum is 1044 bytes`,
                    size: audioBuffer.byteLength
                }), {
                    status: 502,
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    }
                });
            }

            return new Response(audioBuffer, {
                status: 200,
                headers: {
                    'Content-Type': contentType,
                    'Access-Control-Allow-Origin': '*',
                    'Content-Length': audioBuffer.byteLength.toString(),
                    'Cache-Control': 'no-cache'  // Zabráni cache problémom
                }
            });
        } else {
            // Pokúsime sa získať JSON error response
            let errorData;
            try {
                errorData = await oracleResponse.json();
                console.error(`❌ Oracle TTS JSON error: ${oracleResponse.status}`, errorData);
            } catch (jsonError) {
                // Ak nie je JSON, získame text
                const errorText = await oracleResponse.text();
                console.error(`❌ Oracle TTS text error: ${oracleResponse.status} - ${errorText}`);
                errorData = {
                    error: 'TTS failed',
                    details: errorText,
                    status: oracleResponse.status
                };
            }

            return new Response(JSON.stringify(errorData), {
                status: oracleResponse.status,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        }
        
    } catch (error) {
        console.error('❌ TTS proxy error:', error);
        
        return new Response(JSON.stringify({
            error: 'TTS proxy error',
            message: error.message
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
}

export async function onRequestOptions(context) {
    return new Response(null, {
        status: 204,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
}
