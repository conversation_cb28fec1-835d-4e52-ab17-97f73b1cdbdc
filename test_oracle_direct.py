#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script pre priame testovanie Oracle servera
Testuje Piper TTS a Deepgram STT bez Cloudflare proxy
"""

import requests
import json
import os
import tempfile
import subprocess
from pathlib import Path

ORACLE_BASE = "http://127.0.0.1:5000"  # Lokálny server s opravami

def test_oracle_health():
    """Test základnej dostupnosti Oracle servera"""
    print("🏥 Testujem Oracle server health...")
    
    try:
        response = requests.get(f"{ORACLE_BASE}/health", timeout=10)
        print(f"📡 Health response: {response.status_code} {response.reason}")
        print(f"📄 Response text: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_oracle_tts():
    """Test Piper TTS na Oracle serveri"""
    print("\n🔊 Testujem Oracle Piper TTS...")
    
    test_text = "Ahoj! Toto je test Piper TTS na Oracle serveri."
    
    try:
        response = requests.post(
            f"{ORACLE_BASE}/api/speak",
            headers={'Content-Type': 'application/json'},
            json={'text': test_text},
            timeout=30
        )
        
        print(f"📡 TTS response: {response.status_code} {response.reason}")
        print(f"📄 Content-Type: {response.headers.get('content-type')}")
        print(f"📏 Content-Length: {response.headers.get('content-length')}")
        
        if response.status_code == 200:
            audio_size = len(response.content)
            print(f"✅ TTS úspešný: {audio_size} bajtov")
            
            if audio_size < 1044:
                print(f"❌ PROBLÉM: Audio príliš malé ({audio_size}B), očakávané >1044B")
                return False
            else:
                print(f"✅ Audio veľkosť OK: {audio_size} bajtov")
                
                # Uložiť audio súbor pre test
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as f:
                    f.write(response.content)
                    audio_path = f.name
                
                print(f"💾 Audio uložené do: {audio_path}")
                
                # Skúsiť prehrať cez system player (macOS)
                try:
                    subprocess.run(['afplay', audio_path], check=True, timeout=10)
                    print("🎵 Audio prehrané úspešne")
                except subprocess.CalledProcessError:
                    print("⚠️ Nepodarilo sa prehrať audio (afplay)")
                except FileNotFoundError:
                    print("⚠️ afplay nie je dostupný (nie macOS)")
                except subprocess.TimeoutExpired:
                    print("⚠️ Audio prehrávanie timeout")
                
                # Vyčistiť dočasný súbor
                os.unlink(audio_path)
                return True
        else:
            print(f"❌ TTS chyba: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ TTS test failed: {e}")
        return False

def test_oracle_stt():
    """Test Deepgram STT na Oracle serveri"""
    print("\n🎤 Testujem Oracle Deepgram STT...")
    
    # Vytvoríme dummy WebM súbor pre test
    dummy_webm = b'\x1a\x45\xdf\xa3' + b'dummy webm data' * 100  # WebM magic bytes + data
    
    try:
        files = {'audio': ('test.webm', dummy_webm, 'audio/webm')}
        response = requests.post(
            f"{ORACLE_BASE}/api/transcribe",
            files=files,
            timeout=30
        )
        
        print(f"📡 STT response: {response.status_code} {response.reason}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ STT úspešný: {json.dumps(data, indent=2)}")
            return True
        else:
            try:
                error_data = response.json()
                print(f"❌ STT chyba: {json.dumps(error_data, indent=2)}")
            except:
                print(f"❌ STT chyba: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ STT test failed: {e}")
        return False

def test_oracle_chat():
    """Test OpenAI chat na Oracle serveri"""
    print("\n💬 Testujem Oracle OpenAI chat...")
    
    try:
        response = requests.post(
            f"{ORACLE_BASE}/api/chat",
            headers={'Content-Type': 'application/json'},
            json={'message': 'Ahoj! Toto je test správa.'},
            timeout=30
        )
        
        print(f"📡 Chat response: {response.status_code} {response.reason}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chat úspešný: {json.dumps(data, indent=2)}")
            return True
        else:
            try:
                error_data = response.json()
                print(f"❌ Chat chyba: {json.dumps(error_data, indent=2)}")
            except:
                print(f"❌ Chat chyba: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chat test failed: {e}")
        return False

def main():
    """Hlavná funkcia - spustí všetky testy"""
    print("🧪 Oracle Server Direct Test")
    print("=" * 50)
    
    # Pre lokálny server nepotrebujeme SSL warnings
    
    results = {}
    
    # Test 1: Health check
    results['health'] = test_oracle_health()
    
    # Test 2: TTS
    results['tts'] = test_oracle_tts()
    
    # Test 3: STT
    results['stt'] = test_oracle_stt()
    
    # Test 4: Chat
    results['chat'] = test_oracle_chat()
    
    # Súhrn
    print("\n" + "=" * 50)
    print("📊 SÚHRN TESTOV:")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.upper():10} {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    print(f"\nCelkovo: {total_passed}/{total_tests} testov prešlo")
    
    if total_passed == total_tests:
        print("🎉 Všetky testy prešli!")
        return 0
    else:
        print("⚠️ Niektoré testy zlyhali")
        return 1

if __name__ == "__main__":
    exit(main())
