<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cloudflare Functions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Cloudflare Functions</h1>
        <p>Testovanie či Cloudflare Pages Functions fungujú správne</p>

        <!-- Test TTS Function -->
        <div class="test-section">
            <h3>🔊 Test TTS Function</h3>
            <button onclick="testTTS()">Test /api/speak</button>
            <div id="ttsLog" class="log"></div>
            <audio id="ttsAudio" controls style="display: none; width: 100%;"></audio>
        </div>

        <!-- Test Transcribe Function -->
        <div class="test-section">
            <h3>🎤 Test Transcribe Function</h3>
            <button onclick="testTranscribe()">Test /api/transcribe</button>
            <div id="transcribeLog" class="log"></div>
        </div>

        <!-- Test Chat Function -->
        <div class="test-section">
            <h3>💬 Test Chat Function</h3>
            <button onclick="testChat()">Test /api/chat</button>
            <div id="chatLog" class="log"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info', logElement = 'ttsLog') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById(logElement);
            const className = type;
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function testTTS() {
            log('🔊 Testujem Cloudflare TTS Function...', 'info', 'ttsLog');
            
            try {
                const response = await fetch('/api/speak', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: 'Ahoj! Toto je test Cloudflare Functions.' })
                });

                log(`📡 Response: ${response.status} ${response.statusText}`, 'info', 'ttsLog');
                log(`📡 Headers: ${JSON.stringify(Object.fromEntries(response.headers))}`, 'info', 'ttsLog');

                if (response.ok) {
                    const audioBlob = await response.blob();
                    log(`✅ TTS Function funguje! Audio: ${audioBlob.size} bajtov (${audioBlob.type})`, 'success', 'ttsLog');
                    
                    if (audioBlob.size < 1044) {
                        log(`⚠️ PROBLÉM: Audio príliš malé (${audioBlob.size}B)`, 'warning', 'ttsLog');
                    } else {
                        log(`✅ Audio veľkosť OK: ${audioBlob.size} bajtov`, 'success', 'ttsLog');
                        
                        // Prehrať audio
                        const audioUrl = URL.createObjectURL(audioBlob);
                        const audioElement = document.getElementById('ttsAudio');
                        audioElement.src = audioUrl;
                        audioElement.style.display = 'block';
                        audioElement.play();
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ TTS Function chyba: ${errorText}`, 'error', 'ttsLog');
                }
            } catch (error) {
                log(`❌ Network chyba: ${error.message}`, 'error', 'ttsLog');
            }
        }

        async function testTranscribe() {
            log('🎤 Testujem Cloudflare Transcribe Function...', 'info', 'transcribeLog');
            
            // Vytvoríme dummy audio blob pre test
            const dummyAudio = new Blob(['dummy audio data'], { type: 'audio/webm' });
            const formData = new FormData();
            formData.append('audio', dummyAudio, 'test.webm');
            
            try {
                const response = await fetch('/api/transcribe', {
                    method: 'POST',
                    body: formData
                });

                log(`📡 Response: ${response.status} ${response.statusText}`, 'info', 'transcribeLog');

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Transcribe Function funguje! Response: ${JSON.stringify(data)}`, 'success', 'transcribeLog');
                } else {
                    const errorData = await response.json();
                    log(`❌ Transcribe Function chyba: ${JSON.stringify(errorData)}`, 'error', 'transcribeLog');
                }
            } catch (error) {
                log(`❌ Network chyba: ${error.message}`, 'error', 'transcribeLog');
            }
        }

        async function testChat() {
            log('💬 Testujem Cloudflare Chat Function...', 'info', 'chatLog');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: 'Ahoj! Toto je test správa.' })
                });

                log(`📡 Response: ${response.status} ${response.statusText}`, 'info', 'chatLog');

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Chat Function funguje! Response: ${JSON.stringify(data)}`, 'success', 'chatLog');
                } else {
                    const errorData = await response.json();
                    log(`❌ Chat Function chyba: ${JSON.stringify(errorData)}`, 'error', 'chatLog');
                }
            } catch (error) {
                log(`❌ Network chyba: ${error.message}`, 'error', 'chatLog');
            }
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Test stránka načítaná', 'success');
            log(`📡 Testing Functions at: ${window.location.origin}`, 'info');
        });
    </script>
</body>
</html>
