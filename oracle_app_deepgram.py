#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Slovenský hlasový chat server s OpenAI API, Piper TTS a Deepgram STT
Oracle server verzia s reálnym Deepgram SDK
"""

import os
import tempfile
import subprocess
import asyncio
import json
import base64
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import openai
import logging
from deepgram import DeepgramClient, PrerecordedOptions, FileSource

# Konfigurácia logovania
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Povoliť CORS pre všetky domény

# API konfigurácia
openai.api_key = "***************************************************"
openai.organization = "org-BjM6etktd56NAYchMAnPjVMx"

# Deepgram konfigurácia
DEEPGRAM_API_KEY = "****************************************"
deepgram = DeepgramClient(DEEPGRAM_API_KEY)

# Cesta k Piper modelu (pre lokálne testovanie použijeme mock)
PIPER_MODEL_PATH = "/home/<USER>/models/sk_SK-lili-medium.onnx"
PIPER_CONFIG_PATH = "/home/<USER>/models/sk_SK-lili-medium.onnx.json"

# Mock Piper pre lokálne testovanie
USE_MOCK_PIPER = True  # Nastavíme na True pre lokálne testovanie

class VoiceChat:
    def __init__(self):
        self.conversation_history = []
        
    def add_message(self, role, content):
        """Pridá správu do histórie konverzácie"""
        self.conversation_history.append({"role": role, "content": content})
        
    def get_openai_response(self, user_message):
        """Získa odpoveď z OpenAI API"""
        try:
            # Pridáme správu používateľa
            self.add_message("user", user_message)
            
            # Systémová správa pre slovenský kontext
            system_message = {
                "role": "system", 
                "content": "Si užitočný asistent, ktorý odpovedá v slovenčine. Buď priateľský, informatívny a stručný. Odpovedaj prirodzene ako by si rozprával s priateľom. Používaj slovenské výrazy a buď kultúrne citlivý na slovenský kontext."
            }
            
            # Pripravíme správy pre API
            messages = [system_message] + self.conversation_history[-10:]  # Posledných 10 správ
            
            # Volanie OpenAI API (nová verzia)
            from openai import OpenAI
            client = OpenAI(api_key=openai.api_key)

            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=200,
                temperature=0.7
            )

            assistant_message = response.choices[0].message.content.strip()
            self.add_message("assistant", assistant_message)
            
            return assistant_message
            
        except Exception as e:
            logger.error(f"Chyba pri volaní OpenAI API: {e}")
            return "Prepáčte, nastala chyba pri spracovaní vašej požiadavky."
    
    def create_mock_wav(self, text, output_path):
        """Vytvorí mock WAV súbor pre testovanie"""
        # Vytvoríme jednoduchý WAV súbor s tónom
        import wave
        import math

        sample_rate = 16000
        duration = max(1.0, len(text) * 0.1)  # Minimálne 1 sekunda, 0.1s na znak
        frequency = 440  # A4 tón

        # Generujeme tón
        frames = []
        for i in range(int(sample_rate * duration)):
            value = int(32767 * 0.3 * math.sin(2 * math.pi * frequency * i / sample_rate))
            frames.append(value)

        # Zapíšeme WAV súbor
        with wave.open(output_path, 'wb') as wav_file:
            wav_file.setnchannels(1)  # mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(b''.join(frame.to_bytes(2, 'little', signed=True) for frame in frames))

        return output_path

    def text_to_speech(self, text):
        """Konvertuje text na reč pomocou Piper TTS"""
        try:
            # Vytvoríme dočasný súbor
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

            logger.info(f"🔊 Generujem TTS pre text: '{text[:50]}...'")

            if USE_MOCK_PIPER:
                # Mock Piper pre lokálne testovanie
                logger.info("🧪 Používam mock Piper TTS pre lokálne testovanie")
                self.create_mock_wav(text, temp_path)
                file_size = os.path.getsize(temp_path)
                logger.info(f"✅ Mock Piper TTS úspešný: {file_size} bajtov")
                return temp_path
            else:
                # Skutočný Piper TTS
                piper_process = subprocess.Popen(
                    [
                        '/home/<USER>/piper_env/bin/piper',
                        '--model', PIPER_MODEL_PATH,
                        '--output_file', temp_path,
                        '--length_scale', '1.0',      # Normálna rýchlosť
                        '--noise_scale', '0.667',     # Mierna variácia pre prirodzenosť
                        '--noise_w', '0.8',           # Variácia dĺžky fonémov
                        '--sentence_silence', '0.3'   # Pauza medzi vetami
                    ],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                # Pošleme text do stdin a počkáme na dokončenie
                stdout, stderr = piper_process.communicate(input=text)

                if piper_process.returncode == 0:
                    # Skontrolujeme veľkosť vygenerovaného súboru
                    if os.path.exists(temp_path):
                        file_size = os.path.getsize(temp_path)
                        logger.info(f"✅ Piper TTS úspešný: {file_size} bajtov")

                        # Minimálna veľkosť pre platný WAV súbor (44 byte header + aspoň 1000 bajtov audio)
                        if file_size > 1044:
                            return temp_path
                        else:
                            logger.error(f"❌ Piper TTS súbor príliš malý: {file_size} bajtov")
                            os.unlink(temp_path)
                            return None
                    else:
                        logger.error("❌ Piper TTS nevytvoril výstupný súbor")
                        return None
                else:
                    logger.error(f"❌ Piper TTS chyba (kód {piper_process.returncode}): {stderr}")
                    return None

        except Exception as e:
            logger.error(f"❌ Chyba pri generovaní reči: {e}")
            return None

    def detect_audio_format(self, audio_data):
        """Detekuje formát audio súboru na základe magic bytes"""
        if audio_data.startswith(b'RIFF') and b'WAVE' in audio_data[:12]:
            return 'wav'
        elif audio_data.startswith(b'\x1a\x45\xdf\xa3'):  # WebM/Matroska
            return 'webm'
        elif audio_data.startswith(b'OggS'):
            return 'ogg'
        elif audio_data.startswith(b'ID3') or audio_data.startswith(b'\xff\xfb'):
            return 'mp3'
        else:
            return 'unknown'

    def convert_webm_to_wav(self, input_path, output_path):
        """Konvertuje WebM audio na WAV pomocou ffmpeg"""
        try:
            # ffmpeg command pre konverziu WebM -> WAV s optimálnymi nastaveniami pre Deepgram
            cmd = [
                'ffmpeg', '-y',  # -y = overwrite output file
                '-i', input_path,  # input file
                '-ar', '16000',    # sample rate 16kHz (optimálne pre Deepgram)
                '-ac', '1',        # mono channel
                '-c:a', 'pcm_s16le',  # PCM 16-bit little endian
                '-f', 'wav',       # WAV format
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                logger.info(f"✅ WebM -> WAV konverzia úspešná: {output_path}")
                return True
            else:
                logger.error(f"❌ ffmpeg chyba: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ ffmpeg timeout - konverzia trvala príliš dlho")
            return False
        except Exception as e:
            logger.error(f"❌ Chyba pri konverzii WebM->WAV: {e}")
            return False

    async def speech_to_text(self, audio_data):
        """Konvertuje reč na text pomocou Deepgram"""
        try:
            logger.info(f"🎤 Spracovávam audio: {len(audio_data)} bajtov")

            # Detekujeme formát audio súboru
            audio_format = self.detect_audio_format(audio_data)
            logger.info(f"🎵 Detekovaný audio formát: {audio_format}")

            # Vytvoríme dočasný súbor pre pôvodné audio
            with tempfile.NamedTemporaryFile(suffix=f'.{audio_format}', delete=False) as temp_input:
                temp_input.write(audio_data)
                temp_input_path = temp_input.name

            # Ak je to WebM, konvertujeme na WAV
            if audio_format == 'webm':
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_output:
                    temp_wav_path = temp_output.name

                if self.convert_webm_to_wav(temp_input_path, temp_wav_path):
                    # Použijeme konvertovaný WAV súbor
                    os.unlink(temp_input_path)  # Vyčistíme pôvodný súbor
                    temp_path = temp_wav_path
                    logger.info("🔄 Používam konvertovaný WAV súbor pre Deepgram")
                else:
                    # Ak konverzia zlyhala, skúsime pôvodný súbor
                    os.unlink(temp_wav_path)
                    temp_path = temp_input_path
                    logger.warning("⚠️ Konverzia zlyhala, používam pôvodný WebM súbor")
            else:
                # Pre WAV a ostatné formáty používame pôvodný súbor
                temp_path = temp_input_path

            # Skúsime viacero konfigurácií pre lepšie rozpoznávanie
            language_configs = [
                # Slovenčina s automatickou detekciou
                {
                    "model": "nova-2",
                    "language": "sk",
                    "detect_language": True,
                    "smart_format": True,
                    "punctuate": True,
                    "alternatives": 3
                },
                # Čeština (podobná slovenčine)
                {
                    "model": "nova-2",
                    "language": "cs",
                    "smart_format": True,
                    "punctuate": True,
                    "alternatives": 2
                },
                # Angličtina ako fallback
                {
                    "model": "nova-2",
                    "language": "en",
                    "smart_format": True,
                    "punctuate": True,
                    "alternatives": 1
                }
            ]

            # Načítanie audio súboru
            with open(temp_path, "rb") as file:
                buffer_data = file.read()

            # Skontrolujeme veľkosť súboru
            file_size = len(buffer_data)
            logger.info(f"📁 Audio súbor pre Deepgram: {file_size} bajtov")

            if file_size < 100:  # Príliš malý súbor
                logger.error(f"❌ Audio súbor príliš malý pre Deepgram: {file_size} bajtov")
                os.unlink(temp_path)
                return ""

            payload: FileSource = {
                "buffer": buffer_data,
            }

            transcript = ""

            # Skúsime rôzne jazykové konfigurácie
            for i, config in enumerate(language_configs):
                try:
                    logger.info(f"🌍 Skúšam Deepgram s jazykom: {config['language']}")

                    options = PrerecordedOptions(**config)
                    response = deepgram.listen.prerecorded.v("1").transcribe_file(payload, options)

                    # Detailné logovanie odpovede pre debugging
                    logger.info(f"📡 Deepgram response status: {response}")

                    # Extrakcia textu z odpovede
                    if response.results and response.results.channels:
                        alternatives = response.results.channels[0].alternatives
                        if alternatives and len(alternatives) > 0:
                            transcript = alternatives[0].transcript.strip()
                            confidence = alternatives[0].confidence if hasattr(alternatives[0], 'confidence') else 'N/A'

                            if transcript:  # Ak máme neprázdny text
                                logger.info(f"✅ Deepgram úspešná transkripcia ({config['language']}, confidence: {confidence}): '{transcript}'")
                                break
                            else:
                                logger.warning(f"⚠️ Deepgram vrátil prázdny transcript pre jazyk: {config['language']}")
                    else:
                        logger.warning(f"⚠️ Deepgram nevrátil results/channels pre jazyk: {config['language']}")

                except Exception as lang_error:
                    logger.error(f"❌ Chyba pri Deepgram s jazykom {config['language']}: {lang_error}")
                    # Logujeme detaily chyby ak je to možné
                    if hasattr(lang_error, 'response'):
                        logger.error(f"❌ Deepgram API response: {lang_error.response}")
                    continue

            # Vyčistenie dočasného súboru
            os.unlink(temp_path)

            if not transcript:
                logger.warning("❌ Deepgram nerozpoznal žiadny text vo všetkých jazykoch")

            return transcript

        except Exception as e:
            logger.error(f"❌ Chyba pri Deepgram STT: {e}")
            # Vyčistíme dočasné súbory ak existujú
            try:
                if 'temp_path' in locals():
                    os.unlink(temp_path)
            except:
                pass
            return ""

# Globálna inštancia chat-u
voice_chat = VoiceChat()

@app.route('/health')
def health():
    """Health check endpoint"""
    return 'OK - Slovak Voice Chat Server with Deepgram STT on Oracle Cloud is running!', 200

@app.route('/api/chat', methods=['POST'])
def chat():
    """API endpoint pre chat"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': 'Prázdna správa'}), 400
        
        # Získame odpoveď z OpenAI
        response = voice_chat.get_openai_response(user_message)
        
        return jsonify({
            'response': response,
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"Chyba v chat API: {e}")
        return jsonify({'error': 'Chyba servera'}), 500

@app.route('/api/speak', methods=['POST'])
def speak():
    """API endpoint pre text-to-speech"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'error': 'Neplatný JSON',
                'details': 'Request body musí obsahovať platný JSON'
            }), 400

        text = data.get('text', '').strip()

        if not text:
            return jsonify({
                'error': 'Prázdny text',
                'details': 'JSON musí obsahovať neprázdny text field'
            }), 400

        if len(text) > 1000:
            return jsonify({
                'error': 'Text príliš dlhý',
                'details': f'Text má {len(text)} znakov, maximum je 1000'
            }), 400

        logger.info(f"🔊 Speak API: Generujem TTS pre text: '{text[:50]}...'")

        # Generujeme audio súbor
        audio_path = voice_chat.text_to_speech(text)

        if audio_path:
            # Skontrolujeme veľkosť súboru pred odoslaním
            file_size = os.path.getsize(audio_path)
            logger.info(f"✅ Speak API: Odosielam audio súbor ({file_size} bajtov)")

            return send_file(
                audio_path,
                as_attachment=False,  # Zmenené na False pre lepšie prehrávanie v prehliadači
                download_name='response.wav',
                mimetype='audio/wav'
            )
        else:
            logger.error("❌ Speak API: Piper TTS nevrátil audio súbor")
            return jsonify({
                'error': 'Chyba pri generovaní audio',
                'details': 'Piper TTS zlyhal pri generovaní audio súboru'
            }), 500

    except Exception as e:
        logger.error(f"❌ Chyba v speak API: {e}")
        import traceback
        logger.error(f"❌ Stack trace: {traceback.format_exc()}")
        return jsonify({
            'error': 'Chyba servera',
            'details': str(e),
            'type': type(e).__name__
        }), 500

@app.route('/api/transcribe', methods=['POST'])
def transcribe():
    """API endpoint pre speech-to-text pomocou Deepgram"""
    try:
        # Kontrola, či bol nahraný súbor
        if 'audio' not in request.files:
            logger.error("Transcribe API: Žiadny audio súbor v requeste")
            return jsonify({'error': 'Žiadny audio súbor'}), 400

        audio_file = request.files['audio']
        if audio_file.filename == '':
            logger.error("Transcribe API: Prázdny názov súboru")
            return jsonify({'error': 'Prázdny súbor'}), 400

        # Čítanie audio dát
        audio_data = audio_file.read()
        audio_size = len(audio_data)

        logger.info(f"📡 Transcribe API: Prijatý audio súbor ({audio_size} bajtov, typ: {audio_file.content_type})")

        # Kontrola minimálnej veľkosti
        if audio_size < 100:
            logger.error(f"❌ Audio súbor príliš malý: {audio_size} bajtov")
            return jsonify({
                'error': 'Audio súbor príliš malý',
                'details': f'Súbor má len {audio_size} bajtov, minimálne je potrebných 100 bajtov',
                'audio_size': audio_size
            }), 400

        # Asynchrónne volanie Deepgram
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            transcript = loop.run_until_complete(voice_chat.speech_to_text(audio_data))
        except Exception as deepgram_error:
            logger.error(f"❌ Deepgram API chyba: {deepgram_error}")
            loop.close()
            return jsonify({
                'error': 'Deepgram API chyba',
                'details': str(deepgram_error),
                'audio_size': audio_size
            }), 502  # Bad Gateway - external service error
        finally:
            loop.close()

        if transcript and transcript.strip():
            logger.info(f"✅ Transcribe API: Úspešná transkripcia: '{transcript}'")
            return jsonify({
                'transcript': transcript,
                'status': 'success',
                'audio_size': audio_size,
                'audio_type': audio_file.content_type
            })
        else:
            logger.warning(f"⚠️ Transcribe API: Prázdna transkripcia pre audio veľkosti {audio_size} bajtov")
            return jsonify({
                'error': 'Nepodarilo sa rozpoznať reč',
                'details': 'Audio môže byť príliš tiché, krátke, v nepodporovanom formáte alebo neobsahuje reč',
                'audio_size': audio_size,
                'suggestions': [
                    'Skúste hovoriť hlasnejšie a jasnejšie',
                    'Uistite sa, že hovoríte po slovensky, česky alebo anglicky',
                    'Nahrávka by mala trvať aspoň 1 sekundu',
                    'Skontrolujte, či mikrofón funguje správne'
                ]
            }), 400

    except Exception as e:
        logger.error(f"❌ Chyba v transcribe API: {e}")
        import traceback
        logger.error(f"❌ Stack trace: {traceback.format_exc()}")
        return jsonify({
            'error': 'Chyba servera',
            'details': str(e),
            'type': type(e).__name__
        }), 500

@app.route('/api/voice-chat', methods=['POST'])
def voice_chat_endpoint():
    """Kompletný voice chat endpoint - STT + Chat + TTS"""
    try:
        # Kontrola audio súboru
        if 'audio' not in request.files:
            return jsonify({'error': 'Žiadny audio súbor'}), 400
        
        audio_file = request.files['audio']
        audio_data = audio_file.read()
        
        # 1. Speech-to-Text pomocou Deepgram
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        transcript = loop.run_until_complete(voice_chat.speech_to_text(audio_data))
        loop.close()
        
        if not transcript:
            return jsonify({'error': 'Nepodarilo sa rozpoznať reč'}), 400
        
        # 2. Získanie odpovede z OpenAI
        ai_response = voice_chat.get_openai_response(transcript)
        
        # 3. Generovanie hlasu pomocou Piper TTS
        audio_path = voice_chat.text_to_speech(ai_response)
        
        if audio_path:
            # Načítanie audio súboru do base64
            with open(audio_path, 'rb') as f:
                audio_base64 = base64.b64encode(f.read()).decode('utf-8')
            
            # Vyčistenie dočasného súboru
            os.unlink(audio_path)
            
            return jsonify({
                'transcript': transcript,
                'response': ai_response,
                'audio': audio_base64,
                'status': 'success'
            })
        else:
            return jsonify({
                'transcript': transcript,
                'response': ai_response,
                'status': 'success',
                'warning': 'TTS nedostupné'
            })
            
    except Exception as e:
        logger.error(f"Chyba v voice-chat API: {e}")
        return jsonify({'error': 'Chyba servera'}), 500

@app.route('/api/history')
def get_history():
    """Získa históriu konverzácie"""
    return jsonify({
        'history': voice_chat.conversation_history[-20:],  # Posledných 20 správ
        'status': 'success'
    })

@app.route('/api/clear')
def clear_history():
    """Vymaže históriu konverzácie"""
    voice_chat.conversation_history = []
    return jsonify({'status': 'success', 'message': 'História vymazaná'})

@app.route('/api/deepgram-test')
def deepgram_test():
    """Test Deepgram pripojenia"""
    try:
        # Jednoduchý test API kľúča
        test_client = DeepgramClient(DEEPGRAM_API_KEY)
        return jsonify({
            'status': 'success',
            'message': 'Deepgram API kľúč je platný na Oracle serveri',
            'api_key_prefix': DEEPGRAM_API_KEY[:8] + '...',
            'server': 'Oracle Cloud (*************)'
        })
    except Exception as e:
        logger.error(f"Deepgram test chyba: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Deepgram test zlyhal: {str(e)}'
        }), 500

if __name__ == '__main__':
    # Kontrola, či existuje Piper model
    if not os.path.exists(PIPER_MODEL_PATH):
        logger.warning(f"Piper model nenájdený na: {PIPER_MODEL_PATH}")
    
    # Kontrola API kľúčov
    if not openai.api_key:
        logger.error("OPENAI_API_KEY nie je nastavený!")
    
    if not DEEPGRAM_API_KEY:
        logger.error("DEEPGRAM_API_KEY nie je nastavený!")
    
    logger.info("🚀 Spúšťam slovenský hlasový chat server s Deepgram STT na Oracle Cloud...")
    logger.info("📡 Server bude dostupný na porte 5000")
    logger.info("🎤 Deepgram STT pre slovenčinu je aktivovaný")
    logger.info("🔊 Piper TTS slovenský hlas je pripravený")
    
    app.run(debug=False, host='0.0.0.0', port=5000)
