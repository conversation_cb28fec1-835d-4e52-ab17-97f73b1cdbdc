/**
 * Cloudflare Worker - Oracle Backend Proxy
 * Bypasses CORS issues for Oracle voice chat backend
 */

const ORACLE_BACKEND = '129.159.9.170';
const ORACLE_HTTP_PORT = '80';

export default {
  async fetch(request, env, ctx) {
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    try {
      const url = new URL(request.url);
      const path = url.pathname;

      // Health check endpoint
      if (path === '/health') {
        const oracleUrl = `http://${ORACLE_BACKEND}:${ORACLE_HTTP_PORT}/health`;

        try {
          const response = await fetch(oracleUrl, {
            method: 'GET',
            headers: {
              'Accept': 'text/plain',
            },
            // Add timeout
            signal: AbortSignal.timeout(5000)
          });

          if (response.ok) {
            const text = await response.text();

            return new Response(text, {
              status: 200,
              headers: {
                'Access-Control-Allow-Origin': '*',
                'Content-Type': 'text/plain',
              },
            });
          } else {
            throw new Error(`Oracle backend returned ${response.status}`);
          }
        } catch (error) {
          console.error('Health check failed:', error);

          return new Response('Oracle backend unavailable', {
            status: 503,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Content-Type': 'text/plain',
            },
          });
        }
      }

      // Piper TTS endpoint
      if (path === '/api/speak' && request.method === 'POST') {
        const oracleUrl = `http://${ORACLE_BACKEND}:${ORACLE_HTTP_PORT}/api/speak`;

        try {
          // Forward the request body
          const requestBody = await request.text();

          const response = await fetch(oracleUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'audio/wav',
            },
            body: requestBody,
            // Add timeout
            signal: AbortSignal.timeout(10000)
          });

          // Check if response is audio or JSON
          const contentType = response.headers.get('content-type') || '';

          if (contentType.includes('audio/') || contentType.includes('application/octet-stream')) {
            // Return audio file
            const audioBuffer = await response.arrayBuffer();

            return new Response(audioBuffer, {
              status: 200,
              headers: {
                'Access-Control-Allow-Origin': '*',
                'Content-Type': 'audio/wav',
                'Content-Length': audioBuffer.byteLength.toString(),
              },
            });
          } else {
            // Return JSON response (mock or error)
            const jsonData = await response.text();

            return new Response(jsonData, {
              status: response.status,
              headers: {
                'Access-Control-Allow-Origin': '*',
                'Content-Type': 'application/json',
              },
            });
          }
        } catch (error) {
          console.error('TTS request failed:', error);

          return new Response(JSON.stringify({
            error: 'TTS service unavailable',
            message: error.message,
            type: 'mock'
          }), {
            status: 503,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Content-Type': 'application/json',
            },
          });
        }
      }

      // Default 404 for unknown paths
      return new Response('Not Found', {
        status: 404,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'text/plain',
        },
      });

    } catch (error) {
      console.error('Worker error:', error);
      
      return new Response(JSON.stringify({
        error: 'Proxy error',
        message: error.message,
      }), {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json',
        },
      });
    }
  },
};
